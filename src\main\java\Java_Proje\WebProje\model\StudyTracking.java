package Java_Proje.WebProje.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Entity
@Table(name = "study_tracking")
public class StudyTracking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull(message = "<PERSON>rih boş olamaz")
    @Column(name = "study_date", nullable = false)
    private LocalDate studyDate;

    @Column(name = "subject_name")
    private String subjectName; // Ders adı

    @Column(name = "topic")
    private String topic; // Konu

    @Column(name = "study_duration")
    private Integer studyDuration; // Çalışma süresi (dakika)

    @Column(name = "start_time")
    private LocalTime startTime; // Başlangıç saati

    @Column(name = "end_time")
    private LocalTime endTime; // <PERSON>i<PERSON> saati

    @Column(name = "study_type")
    private String studyType; // Çalışma türü (Ders, Ödev, Sınav Hazırlığı, vb.)

    @Column(name = "difficulty_level")
    private String difficultyLevel; // Zorluk seviyesi (Kolay, Orta, Zor)

    @Column(name = "completion_status")
    private String completionStatus; // Tamamlanma durumu (Tamamlandı, Yarım Kaldı, Başlanmadı)

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes; // Notlar

    @Column(name = "homework_completed")
    private Boolean homeworkCompleted; // Ödev tamamlandı mı?

    @Column(name = "exam_score")
    private Integer examScore; // Sınav notu (0-100)

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public StudyTracking() {}

    public StudyTracking(User user, LocalDate studyDate) {
        this.user = user;
        this.studyDate = studyDate;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDate getStudyDate() {
        return studyDate;
    }

    public void setStudyDate(LocalDate studyDate) {
        this.studyDate = studyDate;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Integer getStudyDuration() {
        return studyDuration;
    }

    public void setStudyDuration(Integer studyDuration) {
        this.studyDuration = studyDuration;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public String getStudyType() {
        return studyType;
    }

    public void setStudyType(String studyType) {
        this.studyType = studyType;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getCompletionStatus() {
        return completionStatus;
    }

    public void setCompletionStatus(String completionStatus) {
        this.completionStatus = completionStatus;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Boolean getHomeworkCompleted() {
        return homeworkCompleted;
    }

    public void setHomeworkCompleted(Boolean homeworkCompleted) {
        this.homeworkCompleted = homeworkCompleted;
    }

    public Integer getExamScore() {
        return examScore;
    }

    public void setExamScore(Integer examScore) {
        this.examScore = examScore;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}

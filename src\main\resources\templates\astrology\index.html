<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astroloji - WebProje</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* <PERSON><PERSON><PERSON><PERSON> takip sayfasındaki gibi navbar stilleri */
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
            margin: 0 5px;
            border-radius: 8px;
            padding: 8px 16px !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white !important;
        }

        .navbar-brand {
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            height: 100%;
        }

        .main-content-card {
            min-height: 300px;
        }

        .element-card {
            min-height: 200px;
        }

        .content-card {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .content-card h6 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .feature-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.3);
            height: 100%;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .content-card {
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
        }

        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
        }

        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-star-and-crescent me-3"></i>
                            Astroloji ile Kişisel Takibinizi Birleştirin
                        </h1>
                        <p class="text-muted" th:text="${currentDate}">Bugün</p>
                        <p class="lead">Hoş geldin <span th:text="${user.firstName}">Kullanıcı</span>!
                           Burcun: <strong th:text="${user.zodiacSign != null ? user.zodiacSign : 'Koç'}">Burç</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Features -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <a href="/astrology/zodiac-traits" class="text-decoration-none">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-astronaut"></i>
                        </div>
                        <h6>Burç Özellikleri</h6>
                        <small class="text-muted">Kişilik analizi</small>
                    </div>
                </a>
            </div>

            <div class="col-md-4 mb-3">
                <a href="/astrology/compatibility" class="text-decoration-none">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h6>Uyum Testi</h6>
                        <small class="text-muted">Burç uyumları</small>
                    </div>
                </a>
            </div>





            <div class="col-md-4 mb-3">
                <a href="/astrology/rising-sign-calculator" class="text-decoration-none">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <h6>Yükselen Burç</h6>
                        <small class="text-muted" th:text="${user.risingSign != null ? user.risingSign : 'Hesapla'}">Hesapla</small>
                    </div>
                </a>
            </div>
        </div>

        <!-- Yükselen Burç Bilgileri -->
        <div class="row mb-4" th:if="${user.risingSign != null}">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-arrow-up me-2"></i>
                            Yükselen Burç Bilgileri - <span th:text="${user.risingSign}">Burç</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="content-card">
                                    <h6><i class="fas fa-user me-2"></i>Kişilik Özellikleri</h6>
                                    <p id="rising-personality" th:text="${risingSignInfo.personality}">
                                        Yükselen burcunuz, dış dünyanın sizi nasıl gördüğünü ve ilk izleniminizi belirler.
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="content-card">
                                    <h6><i class="fas fa-star me-2"></i>Yıldız Haritası Etkisi</h6>
                                    <p id="rising-chart" th:text="${risingSignInfo.chartEffect}">
                                        Bu burç, doğum anınızdaki doğu ufkunda yükselen burçtur ve kişiliğinizin dış yansımasını şekillendirir.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-heart me-2"></i>İlişkilerde</h6>
                                    <p id="rising-relationships" th:text="${risingSignInfo.relationships}">
                                        İlk tanışmalarda bu özellikleriniz ön plana çıkar.
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-briefcase me-2"></i>Kariyerde</h6>
                                    <p id="rising-career" th:text="${risingSignInfo.career}">
                                        Profesyonel hayatta bu yönleriniz dikkat çeker.
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-palette me-2"></i>Stil & Görünüm</h6>
                                    <p id="rising-style" th:text="${risingSignInfo.style}">
                                        Giyim tarzınız ve dış görünümünüz bu burcun etkisindedir.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Günlük Burç Yorumu - Genişletilmiş -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card main-content-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sun me-2"></i>
                            Günlük Burç Yorumu
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="content-card">
                            <h6 th:text="${user.zodiacSign != null ? user.zodiacSign : 'Koç'}">Burç</h6>
                            <p class="mb-3" id="horoscope-text" th:text="${dailyHoroscope}">Günlük yorum</p>
                            <button class="btn btn-primary btn-sm" onclick="getNewHoroscope()">
                                <i class="fas fa-sync-alt me-2"></i>Yeni Yorum Al
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ay Fazı ve Etkisi - Dropdown Menü ile -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-moon me-2"></i>
                            Ay Fazı ve Etkisi
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-moon me-2"></i>Ay Fazını Seç</h6>
                                    <select class="form-select" id="moonPhaseSelect" onchange="updateMoonPhaseEffect()">
                                        <option value="">Ay fazını seçin...</option>
                                        <option value="yeniay">Yeni Ay</option>
                                        <option value="hilal">Hilal</option>
                                        <option value="ilkceyrek">İlk Çeyrek</option>
                                        <option value="dolunay">Dolunay</option>
                                        <option value="sonceyrek">Son Çeyrek</option>
                                        <option value="azalanay">Azalan Ay</option>
                                        <option value="karanlıkay">Karanlık Ay</option>
                                        <option value="mavi dolunay">Mavi Dolunay</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="content-card">
                                    <h6><i class="fas fa-info-circle me-2"></i>Detaylı Etki ve Öneriler</h6>
                                    <div id="moonPhaseEffect" class="moon-phase-content">
                                        <p class="text-muted">Lütfen bir ay fazı seçin...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tarot, Renk ve Kristal -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>
                            Rastgele Tarot Kartı, Renk ve Kristal Çek
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="row">
                            <!-- Tarot Kartı -->
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-magic me-2"></i>Tarot Kartı</h6>
                                    <h4 id="tarot-name" th:text="${tarotCard.name}">Kart Adı</h4>
                                    <button class="btn btn-primary" onclick="drawNewCard()">
                                        <i class="fas fa-magic me-2"></i>Rastgele Kart Çek
                                    </button>
                                </div>
                            </div>

                            <!-- Günün Rengi -->
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-palette me-2"></i>Günün Rengi</h6>
                                    <h4 id="daily-color" th:text="${dailyElements.color}">Renk</h4>
                                    <button class="btn btn-primary" onclick="getNewColor()">
                                        <i class="fas fa-palette me-2"></i>Rastgele Renk Çek
                                    </button>
                                </div>
                            </div>

                            <!-- Günün Kristali -->
                            <div class="col-md-4">
                                <div class="content-card">
                                    <h6><i class="fas fa-gem me-2"></i>Günün Kristali</h6>
                                    <h4 id="daily-crystal" th:text="${dailyElements.crystal}">Kristal</h4>
                                    <button class="btn btn-primary" onclick="getNewCrystal()">
                                        <i class="fas fa-gem me-2"></i>Rastgele Kristal Çek
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button class="btn btn-success btn-lg" onclick="generateRecommendations()">
                                <i class="fas fa-lightbulb me-2"></i>Bu Kombinasyonla Önerilerimi Al!
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kişiselleştirilmiş Öneriler -->
        <div class="row" id="recommendations-section" style="display: none;">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Senin İçin Özel Öneriler
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="combination-text">Seçtiğin tarot kartı, renk ve kristal kombinasyonuna göre hazırlandı!</span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="content-card">
                                    <h6><i class="fas fa-dumbbell me-2"></i>Bugün Senin İçin Egzersiz Önerisi</h6>
                                    <p class="mb-0" id="exercise-recommendation">Egzersiz önerisi burada görünecek</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="content-card">
                                    <h6><i class="fas fa-apple-alt me-2"></i>Bugün Senin İçin Beslenme Önerisi</h6>
                                    <p class="mb-0" id="nutrition-recommendation">Beslenme önerisi burada görünecek</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Çok çeşitli burç yorumları
        const horoscopes = [
            "Bugün enerjiniz dorukta! Yeni projelere başlamak, cesur adımlar atmak için mükemmel bir gün. Girişimcilik ruhunuz ön planda.",
            "Duygusal dengeniz güçlü, sevdiklerinizle kaliteli vakit geçirmeye odaklanın. Aile bağlarınız güçlenecek.",
            "Yaratıcılığınız patlamaya hazır! Sanatsal projeler, yeni hobiler keşfetmek için ideal zaman. İlham akıyor.",
            "Finansal konularda dikkatli olun, büyük harcamalardan kaçının. Bütçe planlaması yapma zamanı.",
            "İletişim beceriniz zirvede! Önemli konuşmalar, müzakereler için mükemmel gün. Sözleriniz etkili olacak.",
            "Sağlığınıza özel önem verin, vücudunuzun sinyallerini dinleyin. Dinlenme ve beslenmeye odaklanın.",
            "Yeni fırsatlar ufukta görünüyor! Hazır olun, beklenmedik teklifler gelebilir. Şansınız yaver.",
            "İç sesinizi dinleme zamanı. Meditasyon ve kişisel gelişim aktiviteleri size iyi gelecek.",
            "Sosyal çevreniz genişleyecek, yeni arkadaşlıklar kurabilirsiniz. Networking için ideal gün.",
            "Kariyerinizde önemli gelişmeler olabilir. Patronunuzla konuşma fırsatı yakalayabilirsiniz.",
            "Aşk hayatınızda romantik sürprizler sizi bekliyor. Kalp çarpıntıları yaşayabilirsiniz.",
            "Öğrenme isteğiniz yüksek, yeni bilgiler edinmek için mükemmel zaman. Kurs almayı düşünün.",
            "Sezgileriniz güçlü, içgüdülerinize güvenin. Doğru kararlar vereceksiniz.",
            "Ev ve aile konularında önemli kararlar alabilirsiniz. Dekorasyon değişiklikleri gündemde.",
            "Spor ve fiziksel aktiviteler için motivasyonunuz yüksek. Yeni egzersiz rutinleri deneyin.",
            "Teknoloji ve yeniliklerle ilgili konular ilginizi çekecek. Dijital projeler başlatabilirsiniz.",
            "Seyahat planları yapma zamanı. Uzak yerler sizi çağırıyor, macera dolu günler yaklaşıyor.",
            "Maddi konularda beklenmedik gelişmeler olabilir. Yatırım fırsatlarını değerlendirin.",
            "Yaratıcı projelerinizde büyük ilerleme kaydedeceksiniz. Sanatsal yetenekleriniz parıldayacak.",
            "Arkadaşlarınızdan gelen davetler sosyal hayatınızı renklendirece. Eğlenceli anlar sizi bekliyor."
        ];

        const tarotCards = ["Güneş", "Ay", "Yıldız", "Kılıçlar Ası", "Kupalar Ası", "Değnek Ası", "Tılsım Ası", "Kılıçlar Kralı", "Kupalar Kraliçesi", "Değnek Şövalyesi"];
        const colors = ["Mavi", "Yeşil", "Mor", "Turuncu", "Kırmızı", "Sarı", "Pembe", "Beyaz", "Siyah", "Altın"];
        const crystals = ["Ametist", "Kuvars", "Obsidyen", "Citrin", "Gül Kuvarsi", "Labradorit", "Hematit", "Aventurin", "Fluorit", "Selenit"];

        function getNewHoroscope() {
            const randomHoroscope = horoscopes[Math.floor(Math.random() * horoscopes.length)];
            document.getElementById('horoscope-text').textContent = randomHoroscope;
        }

        function drawNewCard() {
            const randomCard = tarotCards[Math.floor(Math.random() * tarotCards.length)];
            document.getElementById('tarot-name').textContent = randomCard;
        }

        function getNewColor() {
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.getElementById('daily-color').textContent = randomColor;
        }

        function getNewCrystal() {
            const randomCrystal = crystals[Math.floor(Math.random() * crystals.length)];
            document.getElementById('daily-crystal').textContent = randomCrystal;
        }

        // Çok detaylı öneri sistemleri
        const exerciseRecommendations = {
            "Güneş": [
                "açık havada 45 dakika tempolu yürüyüş yap, güneş ışığından faydalanarak vitamin D sentezini artır",
                "sabah koşusu ile güne başla, doğal ışık hormon dengenizi düzenleyecek",
                "açık hava yoga seansı düzenle, güneş selamı pozlarını özellikle uygula",
                "bisiklet turu yap, doğayla bağlantı kurarak endorfin seviyeni yükselt",
                "açık havada pilates yap, taze hava akciğerlerini temizleyecek"
            ],
            "Ay": [
                "gece yoga seansı yap, ay ışığında meditasyon ile iç huzuru bul",
                "yavaş tempolu stretching egzersizleri ile kaslarını gevşet",
                "nefes egzersizleri ve mindfulness meditasyonu uygula",
                "su sporları dene, yüzme havuzunda sakin turlar at",
                "tai chi hareketleri ile enerji akışını dengele"
            ],
            "Yıldız": [
                "hedef odaklı interval antrenmanı yap, yıldızlara uzanır gibi sınırlarını zorla",
                "yeni bir spor dalı öğrenmeye başla, potansiyelini keşfet",
                "motivasyonel müzik eşliğinde cardio antrenmanı yap",
                "grup fitness sınıfına katıl, sosyal enerjiyi spor ile birleştir",
                "outdoor bootcamp antrenmanı ile kendini zorla"
            ],
            "Kılıç": [
                "zihinsel odaklanma gerektiren martial arts antrenmanı yap",
                "satranç oynarken statik egzersizler uygula",
                "koordinasyon gerektiren dans antrenmanı yap",
                "bulmaca çözerken ayakta dur ve hafif hareketler yap",
                "konsantrasyon gerektiren balance board egzersizleri dene"
            ],
            "Kupa": [
                "su aerobiği ile hem eğlen hem egzersiz yap",
                "aqua jogging ile eklemlerini zorlamadan cardio yap",
                "yüzme teknikleri geliştir, farklı stilleri dene",
                "spa'da hidroterapi seansı al, kaslarını rahatlatır",
                "sıcak su banyosu sonrası hafif stretching yap"
            ],
            "Değnek": [
                "yaratıcı dans antrenmanı yap, kendi koreografini oluştur",
                "parkour hareketleri öğren, çeviklik ve yaratıcılığı birleştir",
                "freestyle egzersizler yap, rutinden çık",
                "müzik eşliğinde spontan hareketler yap",
                "sanatsal jimnastik hareketleri dene"
            ],
            "Tılsım": [
                "fonksiyonel fitness antrenmanı yap, günlük yaşamda kullanabileceğin hareketler",
                "ağırlık antrenmanı ile kas kütleni artır",
                "pratik egzersizler ile günlük aktivitelerini güçlendir",
                "crossfit antrenmanı ile hem güç hem dayanıklılık kazan",
                "kettlebell egzersizleri ile tüm vücut antrenmanı yap"
            ]
        };

        const nutritionRecommendations = {
            "Güneş": [
                "vitamin D açısından zengin somon, ton balığı ve yumurta sarısı tüket",
                "güneş ışığında büyümüş portakal, limon gibi C vitamini kaynakları ekle",
                "altın sarısı renkli gıdalar: balkabağı, havuç, tatlı patates tercih et",
                "enerji verici kuruyemişler: badem, ceviz, fındık atıştır",
                "doğal bal ile tatlandırılmış bitki çayları iç"
            ],
            "Ay": [
                "rahatlatıcı papatya, lavanta, melisa çayları iç",
                "yumuşak dokulu gıdalar: yoğurt, muhallebi, smoothie tercih et",
                "gece sindirimi kolay besinler: muz, kiraz, badem sütü tüket",
                "magnezyum açısından zengin koyu yeşil yapraklı sebzeler ekle",
                "kalsiyum kaynakları: süt ürünleri, susam, tahini tüket"
            ],
            "Yıldız": [
                "antioksidan açısından zengin süper gıdalar: chia tohumu, goji berry, spirulina",
                "beyin gücünü artıran omega-3 kaynakları: avokado, somon, ceviz",
                "enerji verici protein kaynakları: quinoa, mercimek, nohut",
                "vitamin ve mineral açısından zengin renkli meyveler tüket",
                "doğal detoks suları: limonlu su, yeşil çay, zencefil çayı"
            ],
            "Kılıç": [
                "beyin fonksiyonlarını destekleyen blueberry, böğürtlen tüket",
                "omega-3 açısından zengin balık yağı takviyesi al",
                "konsantrasyonu artıran koyu çikolata (en az %70 kakao) ye",
                "B vitamini kompleksi: tam tahıllar, yeşil yapraklı sebzeler",
                "zihinsel berraklık için yeşil çay ve ginkgo biloba çayı iç"
            ],
            "Kupa": [
                "bol su iç, günde en az 2.5-3 litre sıvı tüket",
                "sulu meyveler: karpuz, kavun, portakal, greyfurt tercih et",
                "çorba ve sulu yemekler ile sıvı alımını artır",
                "hindiba, maydanoz, kereviz gibi diüretik etkili sebzeler",
                "böbrek fonksiyonlarını destekleyen kuşburnu çayı iç"
            ],
            "Değnek": [
                "yaratıcılığı artıran renkli ve çeşitli gıdalar tüket",
                "egzotik meyveler dene: ejder meyvesi, tutku meyvesi, mango",
                "farklı baharatlar kullan: zerdeçal, kimyon, kırmızı biber",
                "enerji verici doğal şekerler: hurma, incir, üzüm",
                "yeni tarifler dene, mutfakta yaratıcı ol"
            ],
            "Tılsım": [
                "protein açısından zengin et, tavuk, balık, yumurta tüket",
                "kas gelişimini destekleyen süt ürünleri ve peynir ekle",
                "demir kaynakları: kırmızı et, ıspanak, mercimek",
                "kemik sağlığı için kalsiyum ve D vitamini al",
                "pratik ve besleyici atıştırmalıklar: kuruyemiş, protein bar"
            ]
        };

        const colorEffects = {
            "Kırmızı": {
                exercise: ["Yüksek enerjili HIIT antrenmanı yap", "Sprint koşuları ile gücünü test et", "Boks antrenmanı ile stresi at"],
                nutrition: ["Kırmızı meyveler: çilek, kiraz, kırmızı üzüm", "Domates, kırmızı biber gibi likopen kaynakları", "Enerji verici kırmızı et protein kaynakları"]
            },
            "Mavi": [
                "Sakin ve dengeli hareketler: yoga, pilates tercih et",
                "Su sporları ile huzur bul",
                "Nefes egzersizleri ile sakinleş"
            ],
            "Yeşil": [
                "Doğada yürüyüş yap, orman banyosu al",
                "Açık havada egzersiz yap",
                "Yeşil alanları tercih et"
            ],
            "Mor": [
                "Spiritüel egzersizler: meditasyon, yoga",
                "Sezgisel dans hareketleri",
                "Chakra dengeleme egzersizleri"
            ],
            "Turuncu": [
                "Sosyal sporlar: takım oyunları, grup fitness",
                "Yaratıcı dans antrenmanları",
                "Eğlenceli aktiviteler tercih et"
            ],
            "Sarı": [
                "Güneşli havada açık hava sporları",
                "Neşeli müzik eşliğinde egzersiz",
                "Motivasyonel antrenmanlar"
            ],
            "Pembe": [
                "Sevgi dolu partner egzersizleri",
                "Kalp açıcı yoga pozları",
                "Romantik yürüyüşler"
            ],
            "Beyaz": [
                "Temizleyici nefes egzersizleri",
                "Arındırıcı meditasyon seansları",
                "Saf ve temiz hareketler"
            ],
            "Siyah": [
                "Güç antrenmanları",
                "Derin odaklanma egzersizleri",
                "Koruyucu martial arts"
            ],
            "Altın": [
                "Lüks spor salonunda antrenman",
                "Prestijli sporlar: golf, tenis",
                "Başarı odaklı egzersizler"
            ]
        };

        // Detaylı ay fazı etkileri
        const moonPhaseEffects = {
            "yeniay": {
                title: "Yeni Ay - Yeni Başlangıçlar",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-seedling me-2"></i>Genel Etki</h6>
                        <p>Yeni ay, yeni başlangıçlar ve niyetler kurma zamanıdır. Enerji seviyeniz düşük olabilir, bu normal bir durumdur. İçe dönük bir dönemdesiniz.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Duygularınız daha hassas ve içsel. Meditasyon ve kişisel gelişim için mükemmel zaman. Yeni hedefler belirleme konusunda sezgileriniz güçlü.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Yeni projeler için plan yapın ama henüz harekete geçmeyin</li>
                            <li>Günlük tutmaya başlayın, düşüncelerinizi yazın</li>
                            <li>Meditasyon ve yoga yapın</li>
                            <li>Detoks programı başlatın</li>
                            <li>Eski alışkanlıklardan kurtulmak için niyet kurun</li>
                        </ul>

                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Dikkat Edilmesi Gerekenler</h6>
                        <p>Büyük kararlar almaktan kaçının. Enerji seviyeniz düşük olduğu için zorlu işlere girişmeyin. Dinlenmeye öncelik verin.</p>
                    </div>
                `
            },
            "hilal": {
                title: "Hilal - Büyüme ve Gelişim",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-arrow-up me-2"></i>Genel Etki</h6>
                        <p>Enerji seviyeniz artmaya başlıyor. Yeni ay döneminde kurduğunuz niyetler şekil almaya başlar. Umut ve motivasyon yükseliyor.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>İyimserlik ve heyecan duyguları ön planda. Gelecek planları yapma isteği güçlü. Sosyal ilişkilerde daha aktif olmak isteyebilirsiniz.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Yeni projelere küçük adımlarla başlayın</li>
                            <li>Öğrenme ve gelişim aktivitelerine odaklanın</li>
                            <li>Sosyal çevrenizi genişletmeye çalışın</li>
                            <li>Yaratıcı hobiler edinin</li>
                            <li>Sağlıklı beslenme alışkanlıkları geliştirin</li>
                        </ul>

                        <h6><i class="fas fa-star me-2"></i>Fırsat Zamanı</h6>
                        <p>Yeni arkadaşlıklar kurmak, beceriler öğrenmek ve alışkanlık değişiklikleri için ideal dönem. Sabırlı olun, henüz sonuçlar tam görünmeyebilir.</p>
                    </div>
                `
            },
            "ilkceyrek": {
                title: "İlk Çeyrek - Eylem ve Kararlılık",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-fist-raised me-2"></i>Genel Etki</h6>
                        <p>En yüksek enerji seviyenizdesiniz. Harekete geçme, zorluklarla mücadele etme ve kararlı adımlar atma zamanı. Meydan okumalar sizi güçlendirir.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Kararlılık ve azim duyguları dorukta. Bazen sabırsızlık yaşayabilirsiniz. Liderlik özellikleriniz ön plana çıkar.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Zor kararları alın ve uygulayın</li>
                            <li>Spor ve fiziksel aktiviteleri artırın</li>
                            <li>İş hayatında inisiyatif alın</li>
                            <li>Ertelediğiniz projeleri tamamlayın</li>
                            <li>Cesur adımlar atın, risk alın</li>
                        </ul>

                        <h6><i class="fas fa-fire me-2"></i>Güç Zamanı</h6>
                        <p>Engelleri aşmak, hedeflere ulaşmak ve büyük değişiklikler yapmak için en uygun dönem. Enerjinizi doğru yönlendirin.</p>
                    </div>
                `
            },
            "dolunay": {
                title: "Dolunay - Doruk ve Tamamlanma",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-crown me-2"></i>Genel Etki</h6>
                        <p>Duygusal ve enerjetik doruk noktasındasınız. Sezgiler en güçlü seviyede. Tamamlanma, hasat ve sonuçları görme zamanı.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Duygular yoğun ve net. Empati kabiliyetiniz artmış durumda. Bazen aşırı duyarlılık yaşayabilirsiniz. Sezgisel kararlar alabilirsiniz.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Önemli kararları sezgilerinizle alın</li>
                            <li>Yaratıcı projelerinizi tamamlayın</li>
                            <li>Sevdiklerinizle kaliteli vakit geçirin</li>
                            <li>Meditasyon ve spiritüel aktiviteler yapın</li>
                            <li>Başarılarınızı kutlayın</li>
                        </ul>

                        <h6><i class="fas fa-magic me-2"></i>Manifestasyon Gücü</h6>
                        <p>Dileklerinizin gerçekleşme olasılığı en yüksek seviyede. Pozitif düşüncelere odaklanın. Şükretmeyi unutmayın.</p>
                    </div>
                `
            },
            "sonceyrek": {
                title: "Son Çeyrek - Bırakma ve Temizlik",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-broom me-2"></i>Genel Etki</h6>
                        <p>Enerji azalmaya başlıyor. Gereksiz olanları bırakma, temizlik yapma ve değerlendirme zamanı. İçsel huzur arayışı güçleniyor.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Nostaljik duygular ve geçmişe dönük düşünceler artabilir. Bağışlama ve affetme konularında daha açık olabilirsiniz.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Eski eşyalarınızı temizleyin, bağışlayın</li>
                            <li>Toksik ilişkilerden uzaklaşın</li>
                            <li>Kötü alışkanlıkları bırakmaya çalışın</li>
                            <li>Geçmiş deneyimlerden ders çıkarın</li>
                            <li>Dinlenme ve iyileşmeye odaklanın</li>
                        </ul>

                        <h6><i class="fas fa-leaf me-2"></i>Arınma Zamanı</h6>
                        <p>Fiziksel, duygusal ve zihinsel temizlik için ideal dönem. Detoks programları ve arınma ritüelleri uygulayın.</p>
                    </div>
                `
            },
            "azalanay": {
                title: "Azalan Ay - İçe Dönüş ve Değerlendirme",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-search me-2"></i>Genel Etki</h6>
                        <p>Enerji seviyeniz düşüyor, bu normal bir süreç. İçe dönük bir dönemdesiniz. Geçmiş deneyimleri değerlendirme zamanı.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Daha sessiz ve düşünceli hissedebilirsiniz. Yalnızlık ihtiyacı artabilir. Derin düşüncelere dalma eğilimi güçlü.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Kişisel gelişim kitapları okuyun</li>
                            <li>Günlük yazma alışkanlığı edinin</li>
                            <li>Meditasyon ve nefes egzersizleri yapın</li>
                            <li>Sanat ve yaratıcılık aktivitelerine yönelin</li>
                            <li>Doğayla vakit geçirin</li>
                        </ul>

                        <h6><i class="fas fa-brain me-2"></i>Bilgelik Zamanı</h6>
                        <p>Yaşadığınız deneyimlerden çıkarılacak dersler için mükemmel dönem. Sabırlı olun ve kendinizi zorlamamaya özen gösterin.</p>
                    </div>
                `
            },
            "karanlıkay": {
                title: "Karanlık Ay - Derin İçsel Yolculuk",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-moon me-2"></i>Genel Etki</h6>
                        <p>En düşük enerji seviyenizdesiniz. Derin içsel çalışma ve ruhsal arınma zamanı. Gizli yetenekleriniz ortaya çıkabilir.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Derin duygusal temizlik yaşayabilirsiniz. Bilinçaltı mesajlar güçlü. Rüyalarınız anlamlı olabilir.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Bol bol dinlenin ve uyuyun</li>
                            <li>Rüya günlüğü tutun</li>
                            <li>Spiritüel aktivitelere yönelin</li>
                            <li>Derin nefes egzersizleri yapın</li>
                            <li>Sessizlik ve yalnızlığın tadını çıkarın</li>
                        </ul>

                        <h6><i class="fas fa-key me-2"></i>Gizli Güçler</h6>
                        <p>Sezgisel yetenekleriniz en güçlü seviyede. İç sesinizi dinleyin. Bu dönem geçici, yakında yeni enerji gelecek.</p>
                    </div>
                `
            },
            "mavi dolunay": {
                title: "Mavi Dolunay - Nadir Fırsatlar",
                content: `
                    <div class="moon-phase-detail">
                        <h6><i class="fas fa-star me-2"></i>Genel Etki</h6>
                        <p>Çok nadir görülen bu ay fazı, olağanüstü fırsatlar ve dönüşümler getirir. Enerji seviyeniz çok yüksek ve sezgileriniz keskin.</p>

                        <h6><i class="fas fa-heart me-2"></i>Duygusal Etki</h6>
                        <p>Yoğun duygusal deneyimler yaşayabilirsiniz. Aşk, tutku ve yaratıcılık dorukta. Sıra dışı olaylar yaşanabilir.</p>

                        <h6><i class="fas fa-lightbulb me-2"></i>Öneriler</h6>
                        <ul>
                            <li>Büyük hayallerinizi gerçekleştirmeye odaklanın</li>
                            <li>Cesur kararlar alın</li>
                            <li>Yaratıcı projelerinizi hayata geçirin</li>
                            <li>Önemli ilişki kararları alabilirsiniz</li>
                            <li>Spiritüel gelişiminize yatırım yapın</li>
                        </ul>

                        <h6><i class="fas fa-magic me-2"></i>Mucizevi Güç</h6>
                        <p>Bu nadir dönemde manifestasyon gücünüz olağanüstü seviyede. Pozitif niyetlerinizi evrene gönderin. Beklenmedik mucizeler yaşayabilirsiniz.</p>
                    </div>
                `
            }
        };

        function updateMoonPhaseEffect() {
            const selectedPhase = document.getElementById('moonPhaseSelect').value;
            const effectDiv = document.getElementById('moonPhaseEffect');

            if (selectedPhase && moonPhaseEffects[selectedPhase]) {
                const phaseData = moonPhaseEffects[selectedPhase];
                effectDiv.innerHTML = `
                    <h5 class="text-primary mb-3">${phaseData.title}</h5>
                    ${phaseData.content}
                `;
            } else {
                effectDiv.innerHTML = '<p class="text-muted">Lütfen bir ay fazı seçin...</p>';
            }
        }

        function generateRecommendations() {
            const tarot = document.getElementById('tarot-name').textContent;
            const color = document.getElementById('daily-color').textContent;
            const crystal = document.getElementById('daily-crystal').textContent;

            // Rastgele detaylı öneriler seç
            let exercise = "";
            let nutrition = "";

            // Tarot bazlı öneriler
            const tarotKey = Object.keys(exerciseRecommendations).find(key => tarot.includes(key.split(' ')[0])) || "Güneş";
            const exerciseOptions = exerciseRecommendations[tarotKey];
            const nutritionOptions = nutritionRecommendations[tarotKey];

            exercise = exerciseOptions[Math.floor(Math.random() * exerciseOptions.length)];
            nutrition = nutritionOptions[Math.floor(Math.random() * nutritionOptions.length)];

            // Renk etkisi ekle
            const colorExercises = [
                `${color} enerjisi ile uyumlu olarak: `,
                `${color} renginin etkisiyle: `,
                `${color} titreşiminde: `
            ];
            const colorNutritions = [
                ` ${color} renkli gıdaları özellikle tercih et.`,
                ` ${color} tonlarındaki besinleri sofrana ekle.`,
                ` ${color} enerjisini destekleyen gıdalar tüket.`
            ];

            exercise = colorExercises[Math.floor(Math.random() * colorExercises.length)] + exercise;
            nutrition = nutrition + colorNutritions[Math.floor(Math.random() * colorNutritions.length)];

            // Kristal etkisi ekle
            const crystalEffects = [
                ` ${crystal} kristalinin enerjisiyle motivasyonunu artır.`,
                ` ${crystal} titreşiminde daha güçlü hisset.`,
                ` ${crystal} kristalinin desteğiyle odaklan.`
            ];

            exercise += crystalEffects[Math.floor(Math.random() * crystalEffects.length)];
            nutrition += ` ${crystal} kristalinin enerji alanında beslenmen daha etkili olacak.`;

            // Sonuçları göster
            document.getElementById('exercise-recommendation').textContent = exercise;
            document.getElementById('nutrition-recommendation').textContent = nutrition;
            document.getElementById('combination-text').textContent = `${tarot} kartı + ${color} rengi + ${crystal} kristali kombinasyonuna göre hazırlandı!`;

            // Öneriler bölümünü göster
            document.getElementById('recommendations-section').style.display = 'block';

            // Öneriler bölümüne kaydır
            document.getElementById('recommendations-section').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>

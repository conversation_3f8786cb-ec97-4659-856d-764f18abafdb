<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AsTracker - Astroloji & <PERSON>ş<PERSON></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Kişisel takip sayfasındaki gibi navbar stilleri */
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
            margin: 0 5px;
            border-radius: 8px;
            padding: 8px 16px !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white !important;
        }

        .navbar-brand {
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
        }

        .hero-section {
            padding: 100px 0;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .feature-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            height: 100%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-hero {
            background: linear-gradient(45deg, #fd7e14, #ffc107);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-hero:hover {
            color: white;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
        }

        .stats-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 60px 0;
            color: white;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-star-and-crescent me-2"></i>
                AsTracker
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/dashboard}">Ana Sayfa</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/tracking}">Kişisel Takip</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/astrology}">Astroloji</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/profile}">Profilim</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/login}">Giriş</a>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/register}">Kayıt</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <span class="navbar-text me-3">
                            <i class="fas fa-user me-1"></i>
                            <span sec:authentication="name">Kullanıcı</span>
                        </span>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <form th:action="@{/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>Çıkış
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">
                <i class="fas fa-star me-3"></i>
                AsTracker
            </h1>
            <p class="hero-subtitle">
                Astroloji ile Kişisel Takibinizi Birleştirin
            </p>

            <div class="mt-4" sec:authorize="!isAuthenticated()">
                <a th:href="@{/login}" class="btn-hero">
                    <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                </a>
                <br>
                <a th:href="@{/register}" class="btn-hero">
                    <i class="fas fa-user-plus me-2"></i>Kayıt Ol
                </a>
            </div>

            <div class="mt-4" sec:authorize="isAuthenticated()">
                <a th:href="@{/dashboard}" class="btn-hero">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard'a Git
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-weight"></i>
                        </div>
                        <h3 class="mb-3">Kişisel Takip</h3>
                        <p class="text-muted">
                            Kilonuzu, diyetinizi, aktivitelerinizi ve ruh halinizi günlük olarak takip edin.
                            Gelişiminizi grafiklerle görselleştirin.
                        </p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-star-and-crescent"></i>
                        </div>
                        <h3 class="mb-3">Astroloji</h3>
                        <p class="text-muted">
                            Günlük burç yorumlarınızı okuyun, tarot kartı çekin ve
                            kişisel yıldız haritanızı keşfedin.
                        </p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="mb-3">İstatistikler</h3>
                        <p class="text-muted">
                            Geçmiş verilerinizi analiz edin, trendleri görün ve
                            kişisel gelişiminizi ölçün.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="container">
        <div class="stats-section">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <p>Burç İşareti</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">78</div>
                        <p>Tarot Kartı</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">365</div>
                        <p>Günlük Takip</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">∞</div>
                        <p>Kişisel Gelişim</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-star-and-crescent me-2"></i>AsTracker</h5>
                    <p class="mb-0">Kendinizi keşfedin, gelişiminizi takip edin.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 Tüm hakları saklıdır.</p>
                    <small>Spring Boot ile geliştirilmiştir.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        // Ana sayfa özel animasyonları
        document.addEventListener('DOMContentLoaded', function() {
            // Hero section animasyonu
            const heroTitle = document.querySelector('.hero-title');
            const heroSubtitle = document.querySelector('.hero-subtitle');
            const heroButtons = document.querySelectorAll('.btn-hero');

            if (heroTitle) {
                heroTitle.style.opacity = '0';
                heroTitle.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    heroTitle.style.transition = 'all 0.8s ease';
                    heroTitle.style.opacity = '1';
                    heroTitle.style.transform = 'translateY(0)';
                }, 200);
            }

            if (heroSubtitle) {
                heroSubtitle.style.opacity = '0';
                heroSubtitle.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    heroSubtitle.style.transition = 'all 0.8s ease';
                    heroSubtitle.style.opacity = '1';
                    heroSubtitle.style.transform = 'translateY(0)';
                }, 400);
            }

            heroButtons.forEach((btn, index) => {
                btn.style.opacity = '0';
                btn.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    btn.style.transition = 'all 0.8s ease';
                    btn.style.opacity = '1';
                    btn.style.transform = 'translateY(0)';
                }, 600 + (index * 200));
            });

            // Feature cards animasyonu
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 800 + (index * 200));
            });
        });
    </script>
</body>
</html>

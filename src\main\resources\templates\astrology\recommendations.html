<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON>Proje</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Navigation için beyaz arka plan */
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .recommendation-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-dumbbell me-3"></i>
                            Burç Önerileri
                        </h1>
                        <p class="lead">Burcunuza özel diyet ve egzersiz önerileri</p>
                        <a href="/astrology" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Ana Sayfaya Dön
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Burç Seçimi -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            Burç Seçin
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="get">
                            <div class="row">
                                <div class="col-md-8">
                                    <select class="form-select" name="sign" onchange="this.form.submit()">
                                        <option value="">Burç Seçin</option>
                                        <option th:each="sign : ${allSigns}"
                                                th:value="${sign}"
                                                th:text="${sign}"
                                                th:selected="${selectedSign != null and selectedSign == sign}">Burç</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>Önerileri Getir
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Öneriler -->
        <div class="row" th:if="${selectedSign != null}">
            <!-- Diyet Önerileri -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-apple-alt me-2"></i>
                            Diyet Önerileri
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="recommendation-card">
                            <h6><i class="fas fa-utensils me-2"></i>Önerilen Gıdalar</h6>
                            <p th:text="${dietRecommendations.foods}">Gıda önerileri</p>

                            <h6><i class="fas fa-times-circle me-2"></i>Kaçınılması Gerekenler</h6>
                            <p th:text="${dietRecommendations.avoid}">Kaçınılacaklar</p>

                            <h6><i class="fas fa-lightbulb me-2"></i>İpucu</h6>
                            <p th:text="${dietRecommendations.tip}">İpucu</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Egzersiz Önerileri -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-dumbbell me-2"></i>
                            Egzersiz Önerileri
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="recommendation-card">
                            <h6><i class="fas fa-running me-2"></i>Önerilen Egzersizler</h6>
                            <p th:text="${exerciseRecommendations.exercise}">Egzersiz önerileri</p>

                            <h6><i class="fas fa-clock me-2"></i>Süre</h6>
                            <p th:text="${exerciseRecommendations.duration}">Süre</p>

                            <h6><i class="fas fa-lightbulb me-2"></i>İpucu</h6>
                            <p th:text="${exerciseRecommendations.tip}">İpucu</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

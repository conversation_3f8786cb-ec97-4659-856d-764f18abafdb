<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> AsTracker</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }
        
        .btn-save:hover {
            opacity: 0.9;
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            color: white;
        }
        
        .form-floating label {
            color: #667eea;
        }
        
        .form-floating .form-control:focus ~ label {
            color: #667eea;
        }
        
        .password-requirements {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .requirement-item {
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Başlık -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h2><i class="fas fa-key me-2"></i>Şifre Değiştir</h2>
                        <p class="text-muted">Güvenliğiniz için güçlü bir şifre seçin</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Şifre Değiştirme Formu -->
        <div class="row">
            <div class="col-md-6 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            Şifre Güncelleme
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Şifre Gereksinimleri -->
                        <div class="password-requirements">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>Şifre Gereksinimleri
                            </h6>
                            <div class="requirement-item">
                                <i class="fas fa-check text-success me-2"></i>En az 6 karakter uzunluğunda olmalı
                            </div>
                            <div class="requirement-item">
                                <i class="fas fa-check text-success me-2"></i>Güvenliğiniz için karmaşık bir şifre seçin
                            </div>
                            <div class="requirement-item">
                                <i class="fas fa-check text-success me-2"></i>Eski şifrenizden farklı olmalı
                            </div>
                        </div>

                        <form th:action="@{/profile/change-password}" method="post">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="currentPassword" name="currentPassword" 
                                       placeholder="Mevcut Şifre" required>
                                <label for="currentPassword">
                                    <i class="fas fa-lock me-2"></i>Mevcut Şifre
                                </label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="newPassword" name="newPassword" 
                                       placeholder="Yeni Şifre" required minlength="6">
                                <label for="newPassword">
                                    <i class="fas fa-key me-2"></i>Yeni Şifre
                                </label>
                            </div>

                            <div class="form-floating mb-4">
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                       placeholder="Yeni Şifre Tekrar" required minlength="6">
                                <label for="confirmPassword">
                                    <i class="fas fa-key me-2"></i>Yeni Şifre Tekrar
                                </label>
                            </div>

                            <!-- Butonlar -->
                            <div class="text-center">
                                <button type="submit" class="btn-save me-3">
                                    <i class="fas fa-save me-2"></i>Şifreyi Değiştir
                                </button>
                                <a th:href="@{/profile}" class="btn-cancel">
                                    <i class="fas fa-times me-2"></i>İptal
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Güvenlik İpuçları -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Güvenlik İpuçları
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="requirement-item">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Şifrenizi kimseyle paylaşmayın
                        </div>
                        <div class="requirement-item">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Düzenli olarak şifrenizi değiştirin
                        </div>
                        <div class="requirement-item">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Farklı siteler için farklı şifreler kullanın
                        </div>
                        <div class="requirement-item">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Şifrenizi güvenli bir yerde saklayın
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Şifre Doğrulama Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newPassword = document.getElementById('newPassword');
            const confirmPassword = document.getElementById('confirmPassword');
            const form = document.querySelector('form');

            function validatePasswords() {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Şifreler eşleşmiyor!');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }

            newPassword.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);

            form.addEventListener('submit', function(e) {
                validatePasswords();
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    </script>
</body>
</html>

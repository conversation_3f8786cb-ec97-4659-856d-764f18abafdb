package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.DietTracking;
import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.DietTrackingRepository;
import Java_Proje.WebProje.repository.PersonalTrackingRepository;
import Java_Proje.WebProje.service.UserService;
import Java_Proje.WebProje.service.PersonalTrackingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/diet")
public class DietController {

    @Autowired
    private DietTrackingRepository dietTrackingRepository;

    @Autowired
    private PersonalTrackingRepository personalTrackingRepository;

    @Autowired
    private PersonalTrackingService personalTrackingService;

    @Autowired
    private UserService userService;

    /**
     * Diyet takip ana sayfası
     */
    @GetMapping
    public String diet(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        // Son diyet kayıtlarını getir
        List<DietTracking> recentDiets = dietTrackingRepository.findTop10ByUserOrderByDietDateDesc(currentUser);

        // Günlük özet bilgileri
        Map<String, Object> dailySummary = getDailySummary(currentUser, LocalDate.now());

        model.addAttribute("user", currentUser);
        model.addAttribute("currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("dd MMMM yyyy")));
        model.addAttribute("recentDiets", recentDiets);
        model.addAttribute("dailySummary", dailySummary);
        model.addAttribute("newDiet", new DietTracking());

        return "diet/index";
    }

    /**
     * Yeni diyet kaydı ekleme
     */
    @PostMapping("/add")
    public String addDiet(@Valid @ModelAttribute("newDiet") DietTracking diet,
                         BindingResult bindingResult,
                         @RequestParam(value = "dietDate", required = false) String dietDateStr,
                         RedirectAttributes redirectAttributes) {

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "Lütfen gerekli alanları doldurun.");
            return "redirect:/diet";
        }

        try {
            // Her zaman yeni kayıt oluştur
            diet.setId(null);

            // Tarih set et
            if (dietDateStr != null && !dietDateStr.isEmpty()) {
                diet.setDietDate(LocalDate.parse(dietDateStr));
            } else {
                diet.setDietDate(LocalDate.now());
            }

            diet.setUser(currentUser);
            dietTrackingRepository.save(diet);

            redirectAttributes.addFlashAttribute("success", "Diyet kaydı başarıyla eklendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Diyet kaydı eklenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/diet";
    }

    /**
     * Vücut ölçüleri güncelleme
     */
    @PostMapping("/update-measurements")
    public String updateMeasurements(@RequestParam(value = "height", required = false) BigDecimal height,
                                   @RequestParam(value = "waist", required = false) BigDecimal waist,
                                   @RequestParam(value = "chest", required = false) BigDecimal chest,
                                   @RequestParam(value = "weight", required = false) BigDecimal weight,
                                   RedirectAttributes redirectAttributes) {

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            boolean hasUpdate = false;

            // Kullanıcı bilgilerini güncelle - sadece geçerli değerler için
            if (height != null && height.compareTo(BigDecimal.ZERO) > 0) {
                currentUser.setHeight(height);
                hasUpdate = true;
            }
            if (waist != null && waist.compareTo(BigDecimal.ZERO) > 0) {
                currentUser.setWaistCircumference(waist);
                hasUpdate = true;
            }
            if (chest != null && chest.compareTo(BigDecimal.ZERO) > 0) {
                currentUser.setChestCircumference(chest);
                hasUpdate = true;
            }

            if (hasUpdate) {
                userService.updateUser(currentUser);
            }

            // Kilo bilgisi varsa hem diyet kaydına hem de personal tracking'e ekle
            if (weight != null && weight.compareTo(BigDecimal.ZERO) > 0) {
                try {
                    // 1. Diyet kaydına ekle
                    DietTracking todayDiet = new DietTracking();
                    todayDiet.setUser(currentUser);
                    todayDiet.setDietDate(LocalDate.now());
                    todayDiet.setDietNotes("Kilo ölçümü: " + weight + " kg");
                    dietTrackingRepository.save(todayDiet);

                    // 2. Personal tracking'e de ekle (dashboard ve profil için)
                    PersonalTracking weightTracking = new PersonalTracking();
                    weightTracking.setUser(currentUser);
                    weightTracking.setTrackingDate(LocalDate.now());
                    weightTracking.setWeight(weight);
                    weightTracking.setNotes("Diyet panelinden kilo kaydı: " + weight + " kg");
                    personalTrackingService.saveTracking(weightTracking);

                    hasUpdate = true;
                } catch (Exception e) {
                    System.err.println("Kilo kaydı eklenirken hata: " + e.getMessage());
                    // Kilo kaydı hatası olsa bile diğer ölçüler güncellenmiş olabilir
                }
            }

            if (hasUpdate) {
                redirectAttributes.addFlashAttribute("success", "Vücut ölçüleri başarıyla güncellendi!");
            } else {
                redirectAttributes.addFlashAttribute("info", "Güncellenecek geçerli bir değer girilmedi.");
            }

        } catch (Exception e) {
            System.err.println("updateMeasurements error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Vücut ölçüleri güncellenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/diet";
    }

    /**
     * Diyet kaydı silme
     */
    @PostMapping("/delete/{id}")
    public String deleteDiet(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            Optional<DietTracking> diet = dietTrackingRepository.findById(id);
            if (diet.isPresent() && diet.get().getUser().getId().equals(currentUser.getId())) {
                dietTrackingRepository.deleteById(id);
                redirectAttributes.addFlashAttribute("success", "Diyet kaydı başarıyla silindi!");
            } else {
                redirectAttributes.addFlashAttribute("error", "Kayıt bulunamadı veya yetkiniz yok.");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıt silinirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/diet";
    }

    /**
     * Test verilerini temizleme
     */
    @PostMapping("/clear-test-data")
    public String clearTestData(RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            List<DietTracking> userDiets = dietTrackingRepository.findByUserOrderByDietDateDesc(currentUser);
            dietTrackingRepository.deleteAll(userDiets);
            redirectAttributes.addFlashAttribute("success", "Tüm diyet kayıtları temizlendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıtlar temizlenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/diet";
    }

    /**
     * Günlük özet bilgileri
     */
    private Map<String, Object> getDailySummary(User user, LocalDate date) {
        Map<String, Object> summary = new HashMap<>();

        try {
            Double totalCalories = dietTrackingRepository.getTotalCaloriesByUserAndDate(user, date);
            Double totalProtein = dietTrackingRepository.getTotalProteinByUserAndDate(user, date);
            Double totalCarbs = dietTrackingRepository.getTotalCarbohydratesByUserAndDate(user, date);
            Double totalFat = dietTrackingRepository.getTotalFatByUserAndDate(user, date);

            summary.put("totalCalories", totalCalories != null ? totalCalories.intValue() : 0);
            summary.put("totalProtein", totalProtein != null ? totalProtein.intValue() : 0);
            summary.put("totalCarbs", totalCarbs != null ? totalCarbs.intValue() : 0);
            summary.put("totalFat", totalFat != null ? totalFat.intValue() : 0);

            // Vücut ölçüleri - null kontrolü ile güvenli şekilde
            summary.put("height", user.getHeight() != null ? user.getHeight().toString() : "");
            summary.put("waist", user.getWaistCircumference() != null ? user.getWaistCircumference().toString() : "");
            summary.put("chest", user.getChestCircumference() != null ? user.getChestCircumference().toString() : "");

        } catch (Exception e) {
            System.err.println("getDailySummary error: " + e.getMessage());
            e.printStackTrace();

            // Hata durumunda varsayılan değerler
            summary.put("totalCalories", 0);
            summary.put("totalProtein", 0);
            summary.put("totalCarbs", 0);
            summary.put("totalFat", 0);
            summary.put("height", "");
            summary.put("waist", "");
            summary.put("chest", "");
        }

        return summary;
    }

    /**
     * Mevcut oturum açmış kullanıcıyı getirir
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userService.findByUsername(username);
        return userOptional.orElse(null);
    }
}

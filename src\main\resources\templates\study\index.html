<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ders Takip - AsTracker</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .study-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            padding: 12px 15px;
            background: rgba(255,255,255,0.9);
        }

        .form-control:focus, .form-select:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            border: none;
            border-radius: 10px;
        }

        .study-item {
            background: rgba(255,255,255,0.7);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #dc3545;
        }

        .floating-label {
            position: relative;
        }

        .floating-label input,
        .floating-label select,
        .floating-label textarea {
            padding-top: 20px;
        }

        .floating-label label {
            position: absolute;
            top: 15px;
            left: 15px;
            color: #6c757d;
            transition: all 0.3s ease;
            pointer-events: none;
            background: transparent;
        }

        .floating-label input:focus + label,
        .floating-label input:not(:placeholder-shown) + label,
        .floating-label select:focus + label,
        .floating-label select:not([value=""]) + label,
        .floating-label textarea:focus + label,
        .floating-label textarea:not(:placeholder-shown) + label {
            top: 5px;
            font-size: 0.75rem;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <!-- Main Content -->
    <div class="container my-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="study-card">
                    <div class="card-body text-center">
                        <h2 class="mb-3">
                            <i class="fas fa-book me-2 text-primary"></i>
                            Ders Takip Paneli
                        </h2>
                        <p class="text-muted mb-0" th:text="${currentDate}">Bugün</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Quick Study Entry -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="study-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            Hızlı Ders Kaydı
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/study/add}" method="post" th:object="${newStudy}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="date" class="form-control" th:field="*{studyDate}" required>
                                        <label>Tarih</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="text" class="form-control" th:field="*{subjectName}" placeholder=" " required>
                                        <label>Ders Adı</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="text" class="form-control" th:field="*{topic}" placeholder=" ">
                                        <label>Konu</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <select class="form-select" th:field="*{studyType}">
                                            <option value="">Seçiniz</option>
                                            <option value="Ders">Ders</option>
                                            <option value="Ödev">Ödev</option>
                                            <option value="Sınav Hazırlığı">Sınav Hazırlığı</option>
                                            <option value="Proje">Proje</option>
                                            <option value="Araştırma">Araştırma</option>
                                        </select>
                                        <label>Çalışma Türü</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" th:field="*{studyDuration}" min="1" max="600" placeholder=" ">
                                        <label>Süre (dakika)</label>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="floating-label">
                                        <select class="form-select" th:field="*{difficultyLevel}">
                                            <option value="">Seçiniz</option>
                                            <option value="Kolay">Kolay</option>
                                            <option value="Orta">Orta</option>
                                            <option value="Zor">Zor</option>
                                        </select>
                                        <label>Zorluk Seviyesi</label>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="floating-label">
                                        <select class="form-select" th:field="*{completionStatus}">
                                            <option value="">Seçiniz</option>
                                            <option value="Tamamlandı">Tamamlandı</option>
                                            <option value="Yarım Kaldı">Yarım Kaldı</option>
                                            <option value="Başlanmadı">Başlanmadı</option>
                                        </select>
                                        <label>Durum</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="floating-label">
                                        <textarea class="form-control" th:field="*{notes}" rows="3" placeholder=" "></textarea>
                                        <label>Notlar</label>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Kaydet
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Studies -->
        <div class="row" th:if="${!#lists.isEmpty(recentStudies)}">
            <div class="col-12">
                <div class="study-card">
                    <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            Son Ders Kayıtları
                        </h5>
                        <form th:action="@{/study/clear-test-data}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-danger btn-sm" 
                                    onclick="return confirm('Tüm ders kayıtlarını silmek istediğinizden emin misiniz?')">
                                <i class="fas fa-trash me-1"></i>Kayıtları Sil
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tarih</th>
                                        <th>Ders</th>
                                        <th>Konu</th>
                                        <th>Tür</th>
                                        <th>Süre</th>
                                        <th>Durum</th>
                                        <th>İşlem</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="study : ${recentStudies}">
                                        <td th:text="${#temporals.format(study.studyDate, 'dd/MM/yyyy')}">01/01/2024</td>
                                        <td th:text="${study.subjectName}">Matematik</td>
                                        <td th:text="${study.topic ?: '-'}">Türev</td>
                                        <td th:text="${study.studyType ?: '-'}">Ders</td>
                                        <td th:text="${study.studyDuration != null ? study.studyDuration + ' dk' : '-'}">60 dk</td>
                                        <td>
                                            <span th:if="${study.completionStatus == 'Tamamlandı'}" class="badge bg-success">Tamamlandı</span>
                                            <span th:if="${study.completionStatus == 'Yarım Kaldı'}" class="badge bg-warning">Yarım Kaldı</span>
                                            <span th:if="${study.completionStatus == 'Başlanmadı'}" class="badge bg-danger">Başlanmadı</span>
                                            <span th:if="${study.completionStatus == null or study.completionStatus == ''}" class="badge bg-secondary">-</span>
                                        </td>
                                        <td>
                                            <form th:action="@{/study/delete/{id}(id=${study.id})}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                        onclick="return confirm('Bu kaydı silmek istediğinizden emin misiniz?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Data Message -->
        <div class="row" th:if="${#lists.isEmpty(recentStudies)}">
            <div class="col-12">
                <div class="study-card">
                    <div class="card-body text-center">
                        <i class="fas fa-book-open" style="font-size: 4rem; color: #dc3545; margin-bottom: 20px;"></i>
                        <h5>Henüz ders kaydı bulunmuyor</h5>
                        <p class="text-muted">Yukarıdaki formu kullanarak ilk ders kaydınızı oluşturun.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSRF Meta Tags -->
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>
</body>
</html>

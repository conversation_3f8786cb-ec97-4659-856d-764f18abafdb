package Java_Proje.WebProje.service;

import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

@Service
@Transactional
public class UserService implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Spring Security için kullanıcı detaylarını yükler
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsernameOrEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("Kullanıcı bulunamadı: " + username));

        return org.springframework.security.core.userdetails.User.builder()
                .username(user.getUsername())
                .password(user.getPassword())
                .authorities("USER")
                .build();
    }

    /**
     * Yeni kullanıcı kaydı
     */
    public User registerUser(User user) {
        // Kullanıcı adı kontrolü
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("Bu kullanıcı adı zaten kullanılıyor!");
        }

        // Email kontrolü
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("Bu email adresi zaten kullanılıyor!");
        }

        // Şifreyi encode et
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Tarih bilgilerini set et
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        return userRepository.save(user);
    }

    /**
     * Kullanıcı adına göre kullanıcı bulur
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * Email adresine göre kullanıcı bulur
     */
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * Kullanıcı ID'sine göre kullanıcı bulur
     */
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * Kullanıcı bilgilerini günceller
     */
    public User updateUser(User user) {
        user.setUpdatedAt(LocalDateTime.now());
        return userRepository.save(user);
    }

    /**
     * Kullanıcı şifresini değiştirir
     */
    public void changePassword(User user, String newPassword) {
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
    }

    /**
     * Kullanıcı şifresini eski şifre kontrolü ile değiştirir
     */
    public void changePassword(User user, String currentPassword, String newPassword) throws Exception {
        if (!checkPassword(user, currentPassword)) {
            throw new Exception("Mevcut şifre yanlış!");
        }
        changePassword(user, newPassword);
    }

    /**
     * Kullanıcı adının mevcut olup olmadığını kontrol eder
     */
    public boolean isUsernameExists(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * Email adresinin mevcut olup olmadığını kontrol eder
     */
    public boolean isEmailExists(String email) {
        return userRepository.existsByEmail(email);
    }

    /**
     * Şifre doğrulaması yapar
     */
    public boolean checkPassword(User user, String rawPassword) {
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    /**
     * Tüm kullanıcıları getirir (admin için)
     */
    public java.util.List<User> getAllUsers() {
        return userRepository.findAll();
    }

    /**
     * Toplam kullanıcı sayısını döner
     */
    public long getTotalUserCount() {
        return userRepository.count();
    }

    /**
     * Burç işaretine göre kullanıcıları getirir
     */
    public java.util.List<User> getUsersByZodiacSign(String zodiacSign) {
        return userRepository.findByZodiacSign(zodiacSign);
    }

    /**
     * Kullanıcı hesabını siler (şifre doğrulaması ile)
     */
    public void deleteUserAccount(User user, String password) throws Exception {
        if (!checkPassword(user, password)) {
            throw new Exception("Şifre yanlış! Hesap silinemedi.");
        }

        // Kullanıcıyı sil (Cascade ile ilişkili veriler de silinecek)
        userRepository.delete(user);
    }
}

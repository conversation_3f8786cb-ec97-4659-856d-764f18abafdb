package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.PersonalTrackingRepository;
import Java_Proje.WebProje.service.PersonalTrackingService;
import Java_Proje.WebProje.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/tracking")
public class TrackingController {

    @Autowired
    private PersonalTrackingService trackingService;

    @Autowired
    private UserService userService;

    @Autowired
    private PersonalTrackingRepository personalTrackingRepository;

    /**
     * Tracking ana sayfası
     */
    @GetMapping
    public String tracking(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        // Sadece hızlı kayıt verilerini getir (kilo olmayan kayıtlar)
        List<PersonalTracking> monthlyTrackings = personalTrackingRepository.findQuickTrackingsByUser(currentUser);

        // Son kilo kaydını getir (veritabanından fresh data)
        Optional<PersonalTracking> latestWeight = trackingService.getLatestWeightTracking(currentUser);

        // BMI hesapla (eğer boy ve kilo varsa)
        BigDecimal bmi = null;
        String bmiCategory = "";
        Double dailyCalories = null;
        if (currentUser.getHeight() != null && latestWeight.isPresent() && latestWeight.get().getWeight() != null) {
            bmi = calculateBMI(currentUser.getHeight(), latestWeight.get().getWeight());
            bmiCategory = getBMICategory(bmi);

            // Günlük kalori ihtiyacını hesapla
            if (currentUser.getAge() != null && currentUser.getGender() != null) {
                dailyCalories = calculateDailyCalories(currentUser, latestWeight.get().getWeight());
            }
        }

        // Model'e verileri ekle
        model.addAttribute("user", currentUser);
        model.addAttribute("monthlyTrackings", monthlyTrackings);
        model.addAttribute("latestWeight", latestWeight.orElse(null));
        model.addAttribute("bmi", bmi);
        model.addAttribute("bmiCategory", bmiCategory);
        model.addAttribute("dailyCalories", dailyCalories);
        model.addAttribute("currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("dd MMMM yyyy")));
        model.addAttribute("newTracking", new PersonalTracking()); // Her zaman yeni kayıt için

        return "tracking/index";
    }

    /**
     * Yeni tracking kaydı ekleme
     */
    @PostMapping("/add")
    public String addTracking(@Valid @ModelAttribute("newTracking") PersonalTracking tracking,
                             BindingResult bindingResult,
                             @RequestParam(value = "trackingDate", required = false) String trackingDateStr,
                             RedirectAttributes redirectAttributes) {

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "Lütfen gerekli alanları doldurun.");
            return "redirect:/tracking";
        }

        try {
            // Her zaman yeni kayıt oluştur - ID'yi null yap
            tracking.setId(null);

            // Tarih set et
            if (trackingDateStr != null && !trackingDateStr.isEmpty()) {
                tracking.setTrackingDate(LocalDate.parse(trackingDateStr));
            } else {
                tracking.setTrackingDate(LocalDate.now());
            }

            tracking.setUser(currentUser);
            trackingService.saveTracking(tracking);

            redirectAttributes.addFlashAttribute("success", "Kayıt başarıyla eklendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıt eklenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/tracking";
    }

    /**
     * Profil güncelleme (vücut ölçüleri için)
     */
    @PostMapping("/update-profile")
    public String updateProfile(@RequestParam(value = "height", required = false) BigDecimal height,
                               @RequestParam(value = "waistCircumference", required = false) BigDecimal waistCircumference,
                               @RequestParam(value = "chestCircumference", required = false) BigDecimal chestCircumference,
                               @RequestParam(value = "age", required = false) Integer age,
                               @RequestParam(value = "gender", required = false) String gender,
                               @RequestParam(value = "activityLevel", required = false) String activityLevel,
                               @RequestParam(value = "weight", required = false) BigDecimal weight,
                               RedirectAttributes redirectAttributes) {

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            if (height != null) currentUser.setHeight(height);
            if (waistCircumference != null) currentUser.setWaistCircumference(waistCircumference);
            if (chestCircumference != null) currentUser.setChestCircumference(chestCircumference);
            if (age != null) currentUser.setAge(age);
            if (gender != null && !gender.isEmpty()) currentUser.setGender(gender);
            if (activityLevel != null && !activityLevel.isEmpty()) currentUser.setActivityLevel(activityLevel);

            userService.updateUser(currentUser);

            // Kilo bilgisi varsa bugünkü tracking kaydına ekle
            if (weight != null) {
                PersonalTracking todayTracking = new PersonalTracking();
                todayTracking.setUser(currentUser);
                todayTracking.setTrackingDate(LocalDate.now());
                todayTracking.setWeight(weight);

                // Kaydet ve flush yap (hemen veritabanına yaz)
                trackingService.saveTracking(todayTracking);

                // Yeni kilo değerini flash attribute olarak gönder
                redirectAttributes.addFlashAttribute("updatedWeight", weight);
                redirectAttributes.addFlashAttribute("weightUpdated", true);
            }

            // BMI ve kalori hesaplama için flash attribute'lar ekle
            if (height != null && weight != null) {
                BigDecimal bmi = calculateBMI(height, weight);
                if (bmi != null) {
                    redirectAttributes.addFlashAttribute("bmi", bmi.toString());
                    redirectAttributes.addFlashAttribute("bmiCategory", getBMICategory(bmi));
                }

                // Kalori hesaplama için güncellenmiş kullanıcı bilgilerini kullan
                if (currentUser.getAge() != null && currentUser.getGender() != null) {
                    Double dailyCalories = calculateDailyCalories(currentUser, weight);
                    if (dailyCalories != null) {
                        redirectAttributes.addFlashAttribute("dailyCalories", Math.round(dailyCalories));
                    }
                }
            }

            redirectAttributes.addFlashAttribute("success", "Profil bilgileri güncellendi! BMI ve kalori bilgileri hesaplandı.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Profil güncellenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/tracking";
    }

    /**
     * Hızlı kayıtları temizle (sadece kilo olmayan kayıtlar)
     */
    @PostMapping("/clear-test-data")
    public String clearTestData(RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            // Kullanıcının sadece hızlı kayıtlarını sil (kilo olmayan kayıtlar)
            List<PersonalTracking> quickTrackings = personalTrackingRepository.findQuickTrackingsByUser(currentUser);
            personalTrackingRepository.deleteAll(quickTrackings);

            redirectAttributes.addFlashAttribute("success", "Hızlı kayıtlar başarıyla silindi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıtlar silinirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/tracking";
    }

    /**
     * Mevcut kullanıcıyı getir
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userService.findByUsername(username);
        return userOptional.orElse(null);
    }

    /**
     * BMI hesaplama
     */
    private BigDecimal calculateBMI(BigDecimal height, BigDecimal weight) {
        if (height == null || weight == null || height.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        // BMI = kilo / (boy^2) - boy metre cinsinden
        BigDecimal heightInMeters = height.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        BigDecimal heightSquared = heightInMeters.multiply(heightInMeters);
        return weight.divide(heightSquared, 1, RoundingMode.HALF_UP);
    }

    /**
     * BMI kategorisi belirleme
     */
    private String getBMICategory(BigDecimal bmi) {
        if (bmi == null) return "";

        if (bmi.compareTo(BigDecimal.valueOf(18.5)) < 0) {
            return "Zayıf";
        } else if (bmi.compareTo(BigDecimal.valueOf(25)) < 0) {
            return "Normal";
        } else if (bmi.compareTo(BigDecimal.valueOf(30)) < 0) {
            return "Fazla Kilolu";
        } else {
            return "Obez";
        }
    }

    /**
     * Günlük kalori ihtiyacını hesapla (Harris-Benedict formülü)
     */
    private Double calculateDailyCalories(User user, BigDecimal weight) {
        if (user.getAge() == null || user.getGender() == null || user.getHeight() == null || weight == null) {
            return null;
        }

        double bmr; // Bazal Metabolizma Hızı
        double heightCm = user.getHeight().doubleValue();
        double weightKg = weight.doubleValue();
        int age = user.getAge();

        // Harris-Benedict formülü
        if ("M".equalsIgnoreCase(user.getGender())) {
            // Erkek: BMR = 88.362 + (13.397 × kilo) + (4.799 × boy) - (5.677 × yaş)
            bmr = 88.362 + (13.397 * weightKg) + (4.799 * heightCm) - (5.677 * age);
        } else {
            // Kadın: BMR = 447.593 + (9.247 × kilo) + (3.098 × boy) - (4.330 × yaş)
            bmr = 447.593 + (9.247 * weightKg) + (3.098 * heightCm) - (4.330 * age);
        }

        // Aktivite seviyesi çarpanı
        double activityMultiplier = 1.2; // Varsayılan: Sedanter
        if (user.getActivityLevel() != null) {
            switch (user.getActivityLevel().toLowerCase()) {
                case "sedanter":
                    activityMultiplier = 1.2;
                    break;
                case "hafif":
                    activityMultiplier = 1.375;
                    break;
                case "orta":
                    activityMultiplier = 1.55;
                    break;
                case "yoğun":
                    activityMultiplier = 1.725;
                    break;
                case "çok_yoğun":
                    activityMultiplier = 1.9;
                    break;
            }
        }

        return bmr * activityMultiplier;
    }
}

<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>Proje</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Navigation için beyaz arka plan */
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .astrology-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .compatibility-form {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px;
            padding: 30px;
        }

        .compatibility-result {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-top: 20px;
        }

        .compatibility-excellent { background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%); }
        .compatibility-good { background: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%); }
        .compatibility-average { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); }
        .compatibility-difficult { background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); }

        .percentage-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .zodiac-select {
            background: rgba(255,255,255,0.9);
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
        }

        .vs-divider {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #667eea;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="astrology-card">
                    <div class="card-body text-center">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-heart me-3"></i>
                            Burç Uyum Testi
                        </h1>
                        <p class="lead">İki burç arasındaki uyumu keşfedin</p>
                        <a href="/astrology" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Ana Sayfaya Dön
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Uyum Testi Formu -->
        <div class="row">
            <div class="col-12">
                <div class="astrology-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>
                            Uyum Testi
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/astrology/compatibility}" method="post" class="compatibility-form">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="zodiac-select">
                                        <h6><i class="fas fa-user me-2"></i>Birinci Burç</h6>
                                        <select class="form-select" name="sign1" required>
                                            <option value="">Burç Seçin</option>
                                            <option th:each="sign : ${allSigns}"
                                                    th:value="${sign}"
                                                    th:text="${sign}"
                                                    th:selected="${sign1 != null and sign1 == sign}">Burç</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <div class="vs-divider">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                </div>

                                <div class="col-md-5">
                                    <div class="zodiac-select">
                                        <h6><i class="fas fa-user me-2"></i>İkinci Burç</h6>
                                        <select class="form-select" name="sign2" required>
                                            <option value="">Burç Seçin</option>
                                            <option th:each="sign : ${allSigns}"
                                                    th:value="${sign}"
                                                    th:text="${sign}"
                                                    th:selected="${sign2 != null and sign2 == sign}">Burç</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic me-2"></i>Uyumu Test Et
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Uyum Sonucu -->
        <div class="row" th:if="${compatibilityResult != null}">
            <div class="col-12">
                <div class="astrology-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Uyum Sonucu
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="compatibility-result"
                             th:classappend="${compatibilityResult.level == 'Mükemmel' ? 'compatibility-excellent' :
                                             compatibilityResult.level == 'İyi' ? 'compatibility-good' :
                                             compatibilityResult.level == 'Orta' ? 'compatibility-average' : 'compatibility-difficult'}">

                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <h4 th:text="${sign1}">Burç 1</h4>
                                    <div style="font-size: 3rem;">
                                        <span th:switch="${sign1}">
                                            <span th:case="'Koç'">♈</span>
                                            <span th:case="'Boğa'">♉</span>
                                            <span th:case="'İkizler'">♊</span>
                                            <span th:case="'Yengeç'">♋</span>
                                            <span th:case="'Aslan'">♌</span>
                                            <span th:case="'Başak'">♍</span>
                                            <span th:case="'Terazi'">♎</span>
                                            <span th:case="'Akrep'">♏</span>
                                            <span th:case="'Yay'">♐</span>
                                            <span th:case="'Oğlak'">♑</span>
                                            <span th:case="'Kova'">♒</span>
                                            <span th:case="'Balık'">♓</span>
                                        </span>
                                    </div>
                                </div>

                                <div class="col-md-4 text-center">
                                    <div class="percentage-circle">
                                        <span th:text="${compatibilityResult.percentage} + '%'">%</span>
                                    </div>
                                    <h4 th:text="${compatibilityResult.level}">Uyum Seviyesi</h4>
                                </div>

                                <div class="col-md-4 text-center">
                                    <h4 th:text="${sign2}">Burç 2</h4>
                                    <div style="font-size: 3rem;">
                                        <span th:switch="${sign2}">
                                            <span th:case="'Koç'">♈</span>
                                            <span th:case="'Boğa'">♉</span>
                                            <span th:case="'İkizler'">♊</span>
                                            <span th:case="'Yengeç'">♋</span>
                                            <span th:case="'Aslan'">♌</span>
                                            <span th:case="'Başak'">♍</span>
                                            <span th:case="'Terazi'">♎</span>
                                            <span th:case="'Akrep'">♏</span>
                                            <span th:case="'Yay'">♐</span>
                                            <span th:case="'Oğlak'">♑</span>
                                            <span th:case="'Kova'">♒</span>
                                            <span th:case="'Balık'">♓</span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h5>Uyum Analizi</h5>
                                <p class="lead" th:text="${compatibilityResult.description}">Açıklama</p>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="alert alert-info border-0" style="background: rgba(255,255,255,0.3);">
                                        <h6><i class="fas fa-lightbulb me-2"></i>İpucu</h6>
                                        <p class="mb-0" th:if="${compatibilityResult.level == 'Mükemmel'}">
                                            Bu uyum harika! Birbirinizi desteklemeye devam edin.
                                        </p>
                                        <p class="mb-0" th:if="${compatibilityResult.level == 'İyi'}">
                                            Güzel bir uyumunuz var. İletişimi güçlendirin.
                                        </p>
                                        <p class="mb-0" th:if="${compatibilityResult.level == 'Orta'}">
                                            Sabır ve anlayış ile güzel bir ilişki kurabilirsiniz.
                                        </p>
                                        <p class="mb-0" th:if="${compatibilityResult.level == 'Zor'}">
                                            Farklılıklarınızı fırsat olarak görün ve birbirinizden öğrenin.
                                        </p>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="alert alert-warning border-0" style="background: rgba(255,255,255,0.3);">
                                        <h6><i class="fas fa-star me-2"></i>Öneriler</h6>
                                        <ul class="mb-0">
                                            <li>Açık iletişim kurun</li>
                                            <li>Birbirinizin farklılıklarına saygı gösterin</li>
                                            <li>Ortak aktiviteler yapın</li>
                                            <li>Sabırlı ve anlayışlı olun</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function quickTest(sign1, sign2) {
            document.querySelector('select[name="sign1"]').value = sign1;
            document.querySelector('select[name="sign2"]').value = sign2;
            document.querySelector('form').submit();
        }
    </script>
</body>
</html>

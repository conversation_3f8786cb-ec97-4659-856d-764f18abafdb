package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Controller
@RequestMapping("/astrology")
public class AstrologyController {

    @Autowired
    private UserService userService;

    /**
     * Mevcut kullanıcıyı al
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            return userService.findByUsername(authentication.getName()).orElse(null);
        }
        return null;
    }

    /**
     * Ana astroloji sayfası
     */
    @GetMapping
    public String index(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        // Günlük burç yorumu
        String zodiacSign = currentUser.getZodiacSign() != null ? currentUser.getZodiacSign() : "Koç";
        String dailyHoroscope = getDailyHoroscope(zodiacSign);

        // Rastgele tarot kartı
        Map<String, String> tarotCard = getRandomTarotCard();

        // Günün rengi, kristali (sloganı kaldırıldı)
        Map<String, String> dailyElements = getDailyElements();

        // Ay fazı
        Map<String, String> moonPhase = getCurrentMoonPhase();

        // Günlük kişiselleştirilmiş öneriler (tarot + renk + kristal + burç kombinasyonu)
        Map<String, String> dailyRecommendations = getDailyRecommendations(
            currentUser.getZodiacSign(), tarotCard, dailyElements
        );

        // Yükselen burç bilgileri
        Map<String, String> risingSignInfo = null;
        if (currentUser.getRisingSign() != null) {
            risingSignInfo = getRisingSignInfo(currentUser.getRisingSign());
        }

        model.addAttribute("user", currentUser);
        model.addAttribute("dailyHoroscope", dailyHoroscope);
        model.addAttribute("tarotCard", tarotCard);
        model.addAttribute("dailyElements", dailyElements);
        model.addAttribute("moonPhase", moonPhase);
        model.addAttribute("risingSignInfo", risingSignInfo);
        model.addAttribute("dailyRecommendations", dailyRecommendations);
        model.addAttribute("currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("dd MMMM yyyy")));

        return "astrology/index";
    }

    /**
     * Burç özellikleri sayfası
     */
    @GetMapping("/zodiac-traits")
    public String zodiacTraits(@RequestParam(value = "sign", required = false) String sign, Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        if (sign == null || sign.isEmpty()) {
            sign = currentUser.getZodiacSign() != null ? currentUser.getZodiacSign() : "Koç";
        }

        Map<String, String> zodiacTraits = getZodiacTraits(sign);
        List<String> allSigns = getAllZodiacSigns();

        model.addAttribute("user", currentUser);
        model.addAttribute("selectedSign", sign);
        model.addAttribute("zodiacTraits", zodiacTraits);
        model.addAttribute("allSigns", allSigns);

        return "astrology/zodiac-traits";
    }

    /**
     * Burç uyum testi
     */
    @GetMapping("/compatibility")
    public String compatibility(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        List<String> allSigns = getAllZodiacSigns();
        model.addAttribute("user", currentUser);
        model.addAttribute("allSigns", allSigns);

        return "astrology/compatibility";
    }

    @PostMapping("/compatibility")
    public String checkCompatibility(@RequestParam("sign1") String sign1,
                                   @RequestParam("sign2") String sign2,
                                   Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        Map<String, String> compatibilityResult = getCompatibility(sign1, sign2);
        List<String> allSigns = getAllZodiacSigns();

        model.addAttribute("user", currentUser);
        model.addAttribute("allSigns", allSigns);
        model.addAttribute("sign1", sign1);
        model.addAttribute("sign2", sign2);
        model.addAttribute("compatibilityResult", compatibilityResult);

        return "astrology/compatibility";
    }



    /**
     * Yükselen burç hesaplama sayfası
     */
    @GetMapping("/rising-sign-calculator")
    public String risingSignCalculator(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        model.addAttribute("user", currentUser);
        return "astrology/rising-sign-calculator";
    }

    /**
     * Yükselen burç hesaplama
     */
    @PostMapping("/calculate-rising-sign")
    public String calculateRisingSign(@RequestParam("birthDate") String birthDate,
                                    @RequestParam("birthTime") String birthTime,
                                    @RequestParam("birthCity") String birthCity,
                                    @RequestParam("birthCountry") String birthCountry,
                                    Model model,
                                    RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            // Yükselen burç hesaplama (basitleştirilmiş)
            String risingSign = calculateRisingSignFromBirthData(birthDate, birthTime, birthCity);
            System.out.println("Hesaplanan yükselen burç: " + risingSign);

            // Kullanıcının yükselen burcunu kaydet
            currentUser.setRisingSign(risingSign);
            System.out.println("Kullanıcıya set edilen yükselen burç: " + currentUser.getRisingSign());

            userService.updateUser(currentUser);
            System.out.println("Kullanıcı database'e kaydedildi. ID: " + currentUser.getId());

            // Yükselen burç bilgilerini al
            Map<String, String> risingSignInfo = getRisingSignInfo(risingSign);

            model.addAttribute("user", currentUser);
            model.addAttribute("risingSign", risingSign);
            model.addAttribute("risingSignInfo", risingSignInfo);

            redirectAttributes.addFlashAttribute("success", "Yükselen burcunuz başarıyla hesaplandı ve kaydedildi: " + risingSign);

            return "astrology/rising-sign-calculator";
        } catch (Exception e) {
            System.err.println("Yükselen burç hesaplama hatası: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Yükselen burç hesaplanırken hata oluştu: " + e.getMessage());
            return "redirect:/astrology/rising-sign-calculator";
        }
    }



    // Yardımcı metodlar
    private String getDailyHoroscope(String zodiacSign) {
        if (zodiacSign == null) return "Burç bilginizi güncelleyin.";

        // Her burç için özel yorumlar
        Map<String, List<String>> zodiacHoroscopes = new HashMap<>();

        zodiacHoroscopes.put("Koç", Arrays.asList(
            "Bugün Mars'ın güçlü enerjisi sizinle! Yeni projelere başlamak için mükemmel bir gün. Cesaretle adım atın, liderlik özellikleriniz ön plana çıkacak. Ancak aceleci davranmaktan kaçının, sabır gösterin.",
            "Ateş enerjiniz bugün dorukta! Spor yapmak ve fiziksel aktiviteler size iyi gelecek. İş hayatında önemli kararlar alabilirsiniz. Aşk hayatınızda romantik sürprizler olabilir.",
            "Bugün kendinizi oldukça enerjik hissedeceksiniz. Yaratıcı projelerinize odaklanın. Finansal konularda dikkatli olun, aceleci yatırımlardan kaçının. Arkadaşlarınızdan gelen teklifler değerlendirilmeli.",
            "Mars'ın etkisiyle bugün çok aktif olacaksınız. Yeni insanlarla tanışma fırsatı bulabilirsiniz. Sağlığınıza dikkat edin, aşırı yorgunluktan kaçının. Akşam saatlerinde dinlendirici aktiviteler tercih edin."
        ));

        zodiacHoroscopes.put("Boğa", Arrays.asList(
            "Venüs'ün etkisiyle bugün estetik ve güzellik konularında şanslısınız. Maddi konularda istikrarlı adımlar atın. Sevdiklerinizle kaliteli zaman geçirin. Doğayla iç içe olmak size huzur verecek.",
            "Bugün sabır ve kararlılığınız ödüllendirilecek. Uzun süredir planladığınız bir proje sonuçlanabilir. Aşk hayatınızda istikrar arayışınız devam ediyor. Lezzetli yemekler ve güzel müzik ruhunuzu besleyecek.",
            "Toprak enerjiniz bugün size güç veriyor. Pratik çözümler bulma konusunda başarılı olacaksınız. Finansal planlarınızı gözden geçirin. Sanat ve müzikle ilgili aktiviteler size ilham verecek.",
            "Bugün istikrar ve güvenlik arayışınız ön planda. Aile ilişkilerinizde sıcak anlar yaşayabilirsiniz. İş hayatında metodical yaklaşımınız takdir görecek. Akşam saatlerinde kendinizi şımartın."
        ));

        zodiacHoroscopes.put("İkizler", Arrays.asList(
            "Merkür'ün etkisiyle bugün iletişim konusunda çok başarılısınız. Yeni bilgiler öğrenme fırsatı bulacaksınız. Sosyal medyada aktif olun, önemli bağlantılar kurabilirsiniz. Zihinsel aktiviteler size enerji verecek.",
            "Bugün merakınız ve öğrenme isteğiniz dorukta! Kısa seyahatler planlanabilir. Kardeşleriniz veya yakın arkadaşlarınızla önemli konuşmalar yapabilirsiniz. Teknoloji ile ilgili yenilikler dikkatinizi çekecek.",
            "Hava enerjiniz bugün size çok yönlülük kazandırıyor. Aynı anda birden fazla işle ilgilenebilirsiniz. İletişim becerileriniz ön plana çıkacak. Yeni hobiler edinme konusunda şanslısınız.",
            "Bugün zihinsel enerjiniz yüksek! Yazma, okuma ve araştırma yapma konusunda verimli olacaksınız. Sosyal çevrenizde yeni gelişmeler olabilir. Esnekliğiniz size avantaj sağlayacak."
        ));

        zodiacHoroscopes.put("Yengeç", Arrays.asList(
            "Ay'ın etkisiyle bugün duygusal zeka seviyeniz yüksek. Aile ilişkilerinizde önemli gelişmeler olabilir. Sezgilerinizi dinleyin, doğru kararlar alacaksınız. Ev ile ilgili projeler başlatabilirsiniz.",
            "Bugün koruyucu ve şefkatli yanınız ön planda. Sevdiklerinize destek olmak size mutluluk verecek. Geçmişle ilgili anılar canlanabilir. Su sporları veya deniz kenarında vakit geçirmek size iyi gelecek.",
            "Su enerjiniz bugün size derin sezgiler veriyor. Duygusal bağlarınız güçlenecek. Aşk hayatınızda romantik anlar yaşayabilirsiniz. Ev dekorasyonu veya yemek yapma konusunda yaratıcı olacaksınız.",
            "Bugün empati yeteneğiniz dorukta! Başkalarının duygularını anlama konusunda çok başarılısınız. Aile büyüklerinizden değerli tavsiyeler alabilirsiniz. İç huzurunuzu korumaya odaklanın."
        ));

        zodiacHoroscopes.put("Aslan", Arrays.asList(
            "Güneş'in etkisiyle bugün ışıldıyorsunuz! Yaratıcı projelerinizde büyük başarı elde edeceksiniz. Sahne alın ve yeteneklerinizi sergilemekten çekinmeyin. Aşk hayatınızda tutkulu anlar yaşayabilirsiniz.",
            "Bugün doğal liderlik özellikleriniz ön plana çıkıyor. Çevrenizdekilere ilham vereceksiniz. Sanatsal aktiviteler size büyük keyif verecek. Cömertliğiniz takdir görecek, ancak aşırıya kaçmayın.",
            "Ateş enerjiniz bugün sizi çok çekici kılıyor. Sosyal ortamlarda dikkat çekeceksiniz. Oyunculuk, müzik veya dans gibi aktiviteler size enerji verecek. Özgüveniniz zirveye çıkacak.",
            "Bugün kraliyet havasınız tam gaz! Lüks ve güzellik konularında şanslısınız. Çocuklarla vakit geçirmek size mutluluk verecek. Yaratıcı zekânız çözüm odaklı yaklaşımlar getirecek."
        ));

        zodiacHoroscopes.put("Başak", Arrays.asList(
            "Merkür'ün etkisiyle bugün analitik zekânız dorukta! Detaylı planlar yapın ve organize olun. Sağlık konularında önemli kararlar alabilirsiniz. Mükemmeliyetçiliğiniz olumlu sonuçlar getirecek.",
            "Bugün pratik zekânız ve problem çözme yeteneğiniz ön planda. İş hayatında verimli bir gün geçireceksiniz. Temizlik ve düzen konularında motivasyonunuz yüksek. Beslenme alışkanlıklarınızı gözden geçirin.",
            "Toprak enerjiniz bugün size istikrar veriyor. Hizmet etme isteğiniz güçlü, başkalarına yardım etmek size tatmin verecek. Detaylara dikkat etme yeteneğiniz takdir görecek. Doğal ürünler tercih edin.",
            "Bugün eleştirel düşünce yeteneğiniz gelişmiş durumda. Araştırma ve analiz gerektiren işlerde başarılı olacaksınız. Sağlıklı yaşam konusunda yeni bilgiler edinebilirsiniz. Sabırlı yaklaşımınız ödüllendirilecek."
        ));

        zodiacHoroscopes.put("Terazi", Arrays.asList(
            "Venüs'ün etkisiyle bugün estetik anlayışınız dorukta! Güzellik ve sanat konularında şanslısınız. İlişkilerinizde denge arayışınız devam ediyor. Diplomasi yeteneğiniz çözüm getirecek.",
            "Bugün adalet duygunuz ve eşitlik arayışınız ön planda. Ortaklık ilişkilerinde önemli gelişmeler olabilir. Sosyal aktiviteler size enerji verecek. Renk ve tasarım konularında yaratıcısınız.",
            "Hava enerjiniz bugün size sosyal beceriler kazandırıyor. Yeni arkadaşlıklar kurabilirsiniz. Aşk hayatınızda romantik gelişmeler olabilir. Müzik ve dans size huzur verecek.",
            "Bugün uyum ve denge arayışınız güçlü. Çelişkili durumları çözme konusunda başarılı olacaksınız. Güzellik ve moda konularında ilginç fikirler geliştirebilirsiniz. İkili ilişkilerinize odaklanın."
        ));

        zodiacHoroscopes.put("Akrep", Arrays.asList(
            "Plüton'un etkisiyle bugün dönüşüm enerjiniz güçlü! Derinlemesine araştırma yapın ve gizli gerçekleri keşfedin. Sezgileriniz sizi doğru yönlendirecek. Tutkulu yaklaşımınız sonuç getirecek.",
            "Bugün gizemli ve manyetik auranız dorukta! Psikoloji ve insan davranışları konusunda keskin gözlemler yapacaksınız. Finansal konularda önemli kararlar alabilirsiniz. Derin duygusal bağlar kurabilirsiniz.",
            "Su enerjiniz bugün size güçlü sezgiler veriyor. Gizli kalmış konular ortaya çıkabilir. Araştırma ve dedektiflik gerektiren işlerde başarılı olacaksınız. Dönüşüm süreçleriniz hızlanacak.",
            "Bugün yoğun duygusal enerjiniz var. Sadakat ve güven konularında net tavır sergileyeceksiniz. Okültizm ve mistik konular ilginizi çekebilir. İçsel gücünüzü keşfetme zamanı."
        ));

        zodiacHoroscopes.put("Yay", Arrays.asList(
            "Jüpiter'in etkisiyle bugün şansınız açık! Uzak yerlerden haberler alabilirsiniz. Felsefe ve yüksek öğrenim konularında ilerleme kaydedeceksiniz. Macera dolu aktiviteler size enerji verecek.",
            "Bugün özgürlük arayışınız güçlü! Seyahat planları yapabilir veya yeni kültürler keşfedebilirsiniz. Öğretmenlik ve rehberlik yeteneğiniz ön plana çıkacak. İyimserliğiniz çevrenizi etkileyecek.",
            "Ateş enerjiniz bugün size cesaret veriyor. Yeni ufuklar keşfetme isteğiniz güçlü. Yabancı dil öğrenme veya farklı kültürlerle tanışma fırsatı bulabilirsiniz. Spor aktiviteleri size iyi gelecek.",
            "Bugün vizyoner yaklaşımınız ön planda. Gelecek planları yapın ve hedeflerinizi büyütün. Hukuk ve adalet konularında şanslısınız. Doğa sporları ve açık hava aktiviteleri size enerji verecek."
        ));

        zodiacHoroscopes.put("Oğlak", Arrays.asList(
            "Satürn'ün etkisiyle bugün disiplin ve kararlılığınız dorukta! Uzun vadeli planlarınızda önemli adımlar atacaksınız. Otorite figürleriyle olumlu ilişkiler kurabilirsiniz. Sabırlı yaklaşımınız ödüllendirilecek.",
            "Bugün sorumluluklarınızı başarıyla yerine getireceksiniz. Kariyer hedeflerinizde ilerleme kaydedeceksiniz. Geleneksel değerlere saygınız takdir görecek. Yaşlılardan değerli tavsiyeler alabilirsiniz.",
            "Toprak enerjiniz bugün size istikrar veriyor. İş hayatında metodical yaklaşımınız başarı getirecek. Finansal güvenlik konusunda önemli adımlar atabilirsiniz. Dağcılık veya doğa yürüyüşü size iyi gelecek.",
            "Bugün hırsınız ve azminiz ön planda. Zor görevleri başarıyla tamamlayacaksınız. Liderlik pozisyonunda olma şansınız var. Pratik çözümler bulma konusunda yeteneklisiniz."
        ));

        zodiacHoroscopes.put("Kova", Arrays.asList(
            "Uranüs'ün etkisiyle bugün yenilikçi fikirleriniz dorukta! Teknoloji ve bilim konularında ilginç keşifler yapabilirsiniz. Grup çalışmalarında başarılı olacaksınız. Özgün yaklaşımınız takdir görecek.",
            "Bugün insancıl yanınız ön planda. Sosyal sorumluluk projelerinde yer alabilirsiniz. Arkadaş çevrenizde önemli gelişmeler olabilir. Gelecek vizyonunuz çevrenizi etkileyecek.",
            "Hava enerjiniz bugün size özgürlük veriyor. Bağımsızlık arayışınız güçlü. Bilim kurgu ve teknoloji konularında ilginç bilgiler edinebilirsiniz. Elektrikli cihazlarla ilgili yenilikler dikkatinizi çekecek.",
            "Bugün devrimci fikirleriniz var. Toplumsal konularda öncü rol oynayabilirsiniz. Astroloji ve alternatif bilimler ilginizi çekebilir. Farklı düşünce tarzınız size avantaj sağlayacak."
        ));

        zodiacHoroscopes.put("Balık", Arrays.asList(
            "Neptün'ün etkisiyle bugün sezgileriniz çok güçlü! Sanatsal yaratıcılığınız dorukta. Rüyalarınız size önemli mesajlar verebilir. Su sporları veya meditasyon size huzur verecek.",
            "Bugün empati yeteneğiniz olağanüstü! Başkalarının duygularını derinden anlayacaksınız. Müzik ve şiir konularında ilham alabilirsiniz. Spiritüel gelişiminiz hızlanacak.",
            "Su enerjiniz bugün size derin duygular veriyor. Aşk hayatınızda romantik anlar yaşayabilirsiniz. Fotoğrafçılık veya resim gibi sanatsal aktiviteler size keyif verecek. İçsel sesinizi dinleyin.",
            "Bugün hayal gücünüz sınırsız! Yaratıcı projelerinizde büyük ilerleme kaydedeceksiniz. Şefkat ve merhamet duygunuz güçlü. Yardım kuruluşlarıyla ilgili aktiviteler size tatmin verecek."
        ));

        List<String> signHoroscopes = zodiacHoroscopes.getOrDefault(zodiacSign, Arrays.asList(
            "Bugün enerjiniz yüksek, yeni fırsatları değerlendirin.",
            "Sevdiklerinizle kaliteli zaman geçirmek için ideal bir gün.",
            "Yaratıcılığınızı ön plana çıkarın, sanatsal aktiviteler size iyi gelecek."
        ));

        Random random = new Random();
        return signHoroscopes.get(random.nextInt(signHoroscopes.size()));
    }

    private Map<String, String> getRandomTarotCard() {
        List<Map<String, String>> tarotCards = Arrays.asList(
            Map.of("name", "Güneş", "meaning", "Mutluluk, başarı ve pozitif enerji getirir."),
            Map.of("name", "Ay", "meaning", "Sezgilerinizi dinleyin, gizli gerçekler ortaya çıkabilir."),
            Map.of("name", "Yıldız", "meaning", "Umut ve ilham dolu bir dönem başlıyor."),
            Map.of("name", "Kılıçlar Ası", "meaning", "Yeni başlangıçlar ve zihinsel berraklık."),
            Map.of("name", "Kupalar Ası", "meaning", "Aşk ve duygusal tatmin dolu bir dönem."),
            Map.of("name", "Değnek Ası", "meaning", "Yaratıcılık ve yeni projeler için uygun zaman."),
            Map.of("name", "Tılsım Ası", "meaning", "Maddi kazanç ve pratik başarılar.")
        );

        Random random = new Random();
        return tarotCards.get(random.nextInt(tarotCards.size()));
    }

    private Map<String, String> getDailyElements() {
        // Genişletilmiş renk listesi ve açıklamaları
        List<Map<String, String>> colors = Arrays.asList(
            Map.of("name", "Mavi", "meaning", "Huzur ve sakinlik getirir. İletişimi güçlendirir."),
            Map.of("name", "Yeşil", "meaning", "Doğa ile bağlantı kurar. Şifa ve yenilenme enerjisi."),
            Map.of("name", "Mor", "meaning", "Spiritüel gelişimi destekler. Sezgileri güçlendirir."),
            Map.of("name", "Turuncu", "meaning", "Yaratıcılığı artırır. Sosyal enerjinizi yükseltir."),
            Map.of("name", "Kırmızı", "meaning", "Güç ve cesaret verir. Motivasyonu artırır."),
            Map.of("name", "Sarı", "meaning", "Zihinsel berraklık sağlar. Neşe ve iyimserlik getirir."),
            Map.of("name", "Pembe", "meaning", "Sevgi ve şefkati artırır. Kalbi açar."),
            Map.of("name", "Beyaz", "meaning", "Temizlik ve saflık. Yeni başlangıçlar için ideal."),
            Map.of("name", "Siyah", "meaning", "Koruma ve güç. Derin düşünce için uygun."),
            Map.of("name", "Altın", "meaning", "Bolluk ve başarı. Özgüveni artırır.")
        );

        // Genişletilmiş kristal listesi ve açıklamaları
        List<Map<String, String>> crystals = Arrays.asList(
            Map.of("name", "Ametist", "meaning", "Zihinsel berraklık ve spiritüel koruma sağlar."),
            Map.of("name", "Kuvars", "meaning", "Enerjiyi artırır ve temizler. Genel şifa kristali."),
            Map.of("name", "Obsidyen", "meaning", "Koruma ve topraklanma. Negatif enerjiyi uzaklaştırır."),
            Map.of("name", "Citrin", "meaning", "Bolluk ve başarı getirir. Güneş enerjisi taşır."),
            Map.of("name", "Gül Kuvarsi", "meaning", "Sevgi ve şefkati artırır. Kalp çakrasını açar."),
            Map.of("name", "Labradorit", "meaning", "Sezgileri güçlendirir. Dönüşüm enerjisi verir."),
            Map.of("name", "Hematit", "meaning", "Topraklanma ve odaklanma sağlar. Güç verir."),
            Map.of("name", "Aventurin", "meaning", "Şans ve fırsatları çeker. Kalp enerjisini dengeler."),
            Map.of("name", "Fluorit", "meaning", "Zihinsel netlik ve konsantrasyon artırır."),
            Map.of("name", "Selenit", "meaning", "Temizlik ve arınma. Yüksek titreşim enerjisi.")
        );

        Random random = new Random();
        Map<String, String> selectedColor = colors.get(random.nextInt(colors.size()));
        Map<String, String> selectedCrystal = crystals.get(random.nextInt(crystals.size()));

        return Map.of(
            "color", selectedColor.get("name"),
            "colorMeaning", selectedColor.get("meaning"),
            "crystal", selectedCrystal.get("name"),
            "crystalMeaning", selectedCrystal.get("meaning")
        );
    }

    private Map<String, String> getCurrentMoonPhase() {
        List<Map<String, String>> moonPhases = Arrays.asList(
            Map.of("phase", "Yeni Ay", "effect", "Yeni başlangıçlar için ideal zaman. Hedeflerinizi belirleyin."),
            Map.of("phase", "Hilal", "effect", "Büyüme ve gelişim dönemi. Projelerinizi ilerletin."),
            Map.of("phase", "Dolunay", "effect", "Duygular yoğun, sezgilerinizi dinleyin."),
            Map.of("phase", "Son Dördün", "effect", "Bırakma ve temizlik zamanı. Gereksizlerden arının.")
        );

        Random random = new Random();
        return moonPhases.get(random.nextInt(moonPhases.size()));
    }

    /**
     * Günlük kişiselleştirilmiş öneriler - Tarot + Renk + Kristal + Burç kombinasyonu
     */
    private Map<String, String> getDailyRecommendations(String zodiacSign, Map<String, String> tarotCard, Map<String, String> dailyElements) {
        if (zodiacSign == null) zodiacSign = "Koç";

        String tarotName = tarotCard.get("name");
        String color = dailyElements.get("color");
        String crystal = dailyElements.get("crystal");

        // Burç bazlı temel özellikler
        Map<String, String> zodiacBase = getZodiacTraits(zodiacSign);
        String element = zodiacBase.get("element");

        // Kombinasyon bazlı egzersiz önerisi
        String exerciseRecommendation = generateExerciseRecommendation(zodiacSign, tarotName, color, crystal, element);

        // Kombinasyon bazlı beslenme önerisi
        String nutritionRecommendation = generateNutritionRecommendation(zodiacSign, tarotName, color, crystal, element);

        return Map.of(
            "exercise", exerciseRecommendation,
            "nutrition", nutritionRecommendation,
            "combination", "Bugün " + tarotName + " kartı, " + color + " rengi ve " + crystal + " kristali " + zodiacSign + " burcunuz için özel bir enerji yaratıyor."
        );
    }

    /**
     * Kombinasyon bazlı egzersiz önerisi üretir
     */
    private String generateExerciseRecommendation(String zodiacSign, String tarotName, String color, String crystal, String element) {
        StringBuilder recommendation = new StringBuilder();

        // Burç bazlı temel egzersiz
        String baseExercise = getBaseExerciseForSign(zodiacSign);
        recommendation.append(baseExercise);

        // Tarot kartı etkisi
        if (tarotName.contains("Güneş")) {
            recommendation.append(" Güneş enerjisi ile açık havada egzersiz yapın.");
        } else if (tarotName.contains("Ay")) {
            recommendation.append(" Ay enerjisi ile meditasyon ve yoga ekleyin.");
        } else if (tarotName.contains("Yıldız")) {
            recommendation.append(" Yıldız enerjisi ile hedef odaklı antrenman yapın.");
        } else if (tarotName.contains("Kılıç")) {
            recommendation.append(" Zihinsel odaklanma gerektiren egzersizler tercih edin.");
        } else if (tarotName.contains("Kupa")) {
            recommendation.append(" Su sporları veya akışkan hareketler ekleyin.");
        } else if (tarotName.contains("Değnek")) {
            recommendation.append(" Yaratıcı ve dinamik hareketler deneyin.");
        } else if (tarotName.contains("Tılsım")) {
            recommendation.append(" Pratik ve sonuç odaklı egzersizler yapın.");
        }

        // Renk etkisi
        if (color.equals("Kırmızı")) {
            recommendation.append(" Yüksek enerjili aktiviteler ideal.");
        } else if (color.equals("Mavi")) {
            recommendation.append(" Sakin ve dengeli hareketler tercih edin.");
        } else if (color.equals("Yeşil")) {
            recommendation.append(" Doğada egzersiz yapmaya odaklanın.");
        } else if (color.equals("Sarı")) {
            recommendation.append(" Neşeli ve sosyal aktiviteler ekleyin.");
        }

        // Kristal etkisi
        if (crystal.equals("Hematit")) {
            recommendation.append(" Güç antrenmanı yapın.");
        } else if (crystal.equals("Ametist")) {
            recommendation.append(" Zihin-beden bağlantısına odaklanın.");
        } else if (crystal.equals("Citrin")) {
            recommendation.append(" Motivasyonunuzu artıran aktiviteler seçin.");
        }

        return recommendation.toString();
    }

    /**
     * Kombinasyon bazlı beslenme önerisi üretir
     */
    private String generateNutritionRecommendation(String zodiacSign, String tarotName, String color, String crystal, String element) {
        StringBuilder recommendation = new StringBuilder();

        // Burç bazlı temel beslenme
        String baseNutrition = getBaseNutritionForSign(zodiacSign);
        recommendation.append(baseNutrition);

        // Tarot kartı etkisi
        if (tarotName.contains("Güneş")) {
            recommendation.append(" Vitamin D açısından zengin gıdalar ekleyin.");
        } else if (tarotName.contains("Ay")) {
            recommendation.append(" Rahatlatıcı çaylar ve yumuşak gıdalar tercih edin.");
        } else if (tarotName.contains("Yıldız")) {
            recommendation.append(" Antioksidan açısından zengin süper gıdalar tüketin.");
        } else if (tarotName.contains("Kılıç")) {
            recommendation.append(" Beyin gıdaları ve omega-3 ekleyin.");
        } else if (tarotName.contains("Kupa")) {
            recommendation.append(" Bol su için ve sıvı gıdalar tercih edin.");
        } else if (tarotName.contains("Değnek")) {
            recommendation.append(" Enerji verici doğal şekerler ekleyin.");
        } else if (tarotName.contains("Tılsım")) {
            recommendation.append(" Protein ve mineral açısından zengin gıdalar seçin.");
        }

        // Renk etkisi
        if (color.equals("Kırmızı")) {
            recommendation.append(" Kırmızı meyveler ve sebzeler ideal.");
        } else if (color.equals("Yeşil")) {
            recommendation.append(" Yeşil yapraklı sebzeler artırın.");
        } else if (color.equals("Turuncu")) {
            recommendation.append(" Beta-karoten açısından zengin gıdalar ekleyin.");
        } else if (color.equals("Mor")) {
            recommendation.append(" Mor renkli antioksidan gıdalar tercih edin.");
        }

        // Kristal etkisi
        if (crystal.equals("Citrin")) {
            recommendation.append(" Sindirim dostu gıdalar seçin.");
        } else if (crystal.equals("Ametist")) {
            recommendation.append(" Detoks etkili gıdalar ekleyin.");
        } else if (crystal.equals("Gül Kuvarsi")) {
            recommendation.append(" Kalp sağlığını destekleyen gıdalar tercih edin.");
        }

        return recommendation.toString();
    }

    /**
     * Burç bazlı temel egzersiz önerisi
     */
    private String getBaseExerciseForSign(String zodiacSign) {
        Map<String, String> baseExercises = new HashMap<>();
        baseExercises.put("Koç", "Yoğun kardiyovasküler egzersizler ve güç antrenmanı.");
        baseExercises.put("Boğa", "Yoga, pilates ve doğa yürüyüşü.");
        baseExercises.put("İkizler", "Çeşitli aktiviteler ve sosyal sporlar.");
        baseExercises.put("Yengeç", "Yüzme ve su sporları.");
        baseExercises.put("Aslan", "Dans ve gösterişli sporlar.");
        baseExercises.put("Başak", "Sistematik antrenman programları.");
        baseExercises.put("Terazi", "Partner egzersizleri ve estetik sporlar.");
        baseExercises.put("Akrep", "Yoğun ve zorlu antrenmanlar.");
        baseExercises.put("Yay", "Açık hava sporları ve macera aktiviteleri.");
        baseExercises.put("Oğlak", "Disiplinli ve uzun vadeli antrenman programları.");
        baseExercises.put("Kova", "Grup fitness ve yenilikçi aktiviteler.");
        baseExercises.put("Balık", "Yoga, meditasyon ve su sporları.");
        return baseExercises.getOrDefault(zodiacSign, "Düzenli fiziksel aktivite.");
    }

    /**
     * Burç bazlı temel beslenme önerisi
     */
    private String getBaseNutritionForSign(String zodiacSign) {
        Map<String, String> baseNutrition = new HashMap<>();
        baseNutrition.put("Koç", "Protein açısından zengin gıdalar ve demir içeren besinler.");
        baseNutrition.put("Boğa", "Organik ve doğal gıdalar, taze sebze ve meyveler.");
        baseNutrition.put("İkizler", "Beyin gıdaları ve omega-3 açısından zengin beslenme.");
        baseNutrition.put("Yengeç", "Deniz ürünleri ve kalsiyum açısından zengin gıdalar.");
        baseNutrition.put("Aslan", "Kalp sağlığını destekleyen gıdalar ve renkli meyveler.");
        baseNutrition.put("Başak", "Lifli gıdalar ve sindirim dostu beslenme.");
        baseNutrition.put("Terazi", "Dengeli beslenme ve böbrek sağlığını destekleyen gıdalar.");
        baseNutrition.put("Akrep", "Detoks etkili gıdalar ve antioksidanlar.");
        baseNutrition.put("Yay", "Çeşitli mutfaklar ve karaciğer sağlığını destekleyen gıdalar.");
        baseNutrition.put("Oğlak", "Kemik sağlığını destekleyen kalsiyum ve magnezyum.");
        baseNutrition.put("Kova", "Dolaşım sistemini destekleyen gıdalar ve antioksidanlar.");
        baseNutrition.put("Balık", "Omega-3 açısından zengin balık ve yumuşak dokulu gıdalar.");
        return baseNutrition.getOrDefault(zodiacSign, "Dengeli ve sağlıklı beslenme.");
    }

    private List<String> getAllZodiacSigns() {
        return Arrays.asList(
            "Koç", "Boğa", "İkizler", "Yengeç", "Aslan", "Başak",
            "Terazi", "Akrep", "Yay", "Oğlak", "Kova", "Balık"
        );
    }

    private Map<String, String> getZodiacTraits(String sign) {
        Map<String, Map<String, String>> traits = new HashMap<>();

        traits.put("Koç", Map.of(
            "element", "Ateş",
            "traits", "Cesur, enerjik, lider ruhlu, aceleci, girişimci, bağımsız, rekabetçi, spontan"
        ));

        traits.put("Boğa", Map.of(
            "element", "Toprak",
            "traits", "Sabırlı, kararlı, güvenilir, inatçı, pratik, sadık, sanatsal, maddi güvenlik odaklı"
        ));

        traits.put("İkizler", Map.of(
            "element", "Hava",
            "traits", "Zeki, konuşkan, meraklı, değişken, sosyal, çok yönlü, hızlı öğrenen, iletişim odaklı"
        ));

        traits.put("Yengeç", Map.of(
            "element", "Su",
            "traits", "Duygusal, koruyucu, sezgisel, hassas, aile odaklı, empatik, nostaljik, şefkatli"
        ));

        traits.put("Aslan", Map.of(
            "element", "Ateş",
            "traits", "Gururlu, yaratıcı, cömert, dramatik, karizmatik, lider, gösterişli, sadık"
        ));

        traits.put("Başak", Map.of(
            "element", "Toprak",
            "traits", "Detaycı, pratik, mükemmeliyetçi, eleştirel, analitik, hizmet odaklı, düzenli, güvenilir"
        ));

        traits.put("Terazi", Map.of(
            "element", "Hava",
            "traits", "Diplomatik, uyumlu, adil, kararsız, estetik, sosyal, romantik, denge arayışında"
        ));

        traits.put("Akrep", Map.of(
            "element", "Su",
            "traits", "Yoğun, gizemli, tutkulu, dönüştürücü, sezgisel, sadık, güçlü, derinlemesine düşünen"
        ));

        traits.put("Yay", Map.of(
            "element", "Ateş",
            "traits", "Özgür, iyimser, maceracı, felsefik, dürüst, öğretici, seyahat seven, açık fikirli"
        ));

        traits.put("Oğlak", Map.of(
            "element", "Toprak",
            "traits", "Disiplinli, sorumlu, hırslı, geleneksel, sabırlı, başarı odaklı, pratik, güvenilir"
        ));

        traits.put("Kova", Map.of(
            "element", "Hava",
            "traits", "Özgün, yenilikçi, bağımsız, insancıl, teknoloji odaklı, arkadaş canlısı, vizyoner, özgür"
        ));

        traits.put("Balık", Map.of(
            "element", "Su",
            "traits", "Hassas, sezgisel, şefkatli, sanatsal, spiritüel, empatik, hayal gücü yüksek, fedakar"
        ));

        return traits.getOrDefault(sign, Map.of("element", "Bilinmiyor", "traits", "Burç bilgisi bulunamadı"));
    }



    private Map<String, String> getCompatibility(String sign1, String sign2) {
        // Basit uyum hesaplama
        int compatibility = Math.abs(sign1.hashCode() + sign2.hashCode()) % 100;
        String level;
        String description;

        if (compatibility > 80) {
            level = "Mükemmel";
            description = "Bu iki burç arasında harika bir uyum var! Birbirinizi çok iyi anlıyorsunuz.";
        } else if (compatibility > 60) {
            level = "İyi";
            description = "Güzel bir uyumunuz var. Küçük farklılıklar ilişkinizi renklendiriyor.";
        } else if (compatibility > 40) {
            level = "Orta";
            description = "Uyumunuz orta seviyede. Anlayış ve sabırla güzel bir ilişki kurabilirsiniz.";
        } else {
            level = "Zor";
            description = "Farklılıklarınız çok ama bu da öğrenme fırsatı yaratıyor.";
        }

        return Map.of(
            "percentage", String.valueOf(compatibility),
            "level", level,
            "description", description
        );
    }

    private Map<String, String> getDietRecommendations(String sign) {
        Map<String, Map<String, String>> recommendations = new HashMap<>();

        recommendations.put("Koç", Map.of(
            "foods", "Protein açısından zengin gıdalar, kırmızı et, baharatlı yemekler, demir içeren besinler",
            "avoid", "Aşırı kafein ve şekerli içeceklerden kaçının, çok sıcak yemekler tüketmeyin",
            "tip", "Hızlı metabolizmanız var, düzenli öğünler önemli. Enerji seviyenizi dengede tutun."
        ));

        recommendations.put("Boğa", Map.of(
            "foods", "Taze sebzeler, organik meyveler, tam tahıllar, kaliteli zeytinyağı, bal",
            "avoid", "İşlenmiş gıdalar, aşırı tatlı ve yağlı yemeklerden uzak durun",
            "tip", "Yavaş yemek yiyin, sindirimi destekleyin. Doğal ve organik ürünleri tercih edin."
        ));

        recommendations.put("İkizler", Map.of(
            "foods", "Omega-3 açısından zengin balık, ceviz, beyin gıdaları, çeşitli meyveler",
            "avoid", "Monoton beslenme tarzından kaçının, aşırı işlenmiş gıdalardan uzak durun",
            "tip", "Çeşitli gıdalar tüketin, su içmeyi unutmayın. Zihinsel performansı destekleyin."
        ));

        recommendations.put("Yengeç", Map.of(
            "foods", "Deniz ürünleri, süt ürünleri, kalsiyum açısından zengin gıdalar, yumuşak dokulu yemekler",
            "avoid", "Çok baharatlı ve asitli yemeklerden kaçının, mide rahatsızlığı yaratabilir",
            "tip", "Duygusal durumunuz beslenmenizi etkiler. Rahatlatıcı çaylar için."
        ));

        recommendations.put("Aslan", Map.of(
            "foods", "Kalp sağlığını destekleyen gıdalar, portakal, limon, altın sarısı meyveler",
            "avoid", "Aşırı yağlı yemekler ve kolesterolü yüksek gıdalardan kaçının",
            "tip", "Kalp sağlığınıza dikkat edin. Renkli ve çekici sunumlu yemekleri tercih edin."
        ));

        recommendations.put("Başak", Map.of(
            "foods", "Lifli gıdalar, probiyotik yoğurt, sindirim dostu sebzeler, tam tahıllar",
            "avoid", "Ağır ve yağlı yemeklerden kaçının, sindirim sisteminizi zorlayabilir",
            "tip", "Sindirim sağlığınıza özen gösterin. Düzenli ve sağlıklı beslenin."
        ));

        recommendations.put("Terazi", Map.of(
            "foods", "Böbrek sağlığını destekleyen gıdalar, bol su, antioksidan açısından zengin meyveler",
            "avoid", "Aşırı tuzlu gıdalar ve alkol tüketimini sınırlayın",
            "tip", "Denge önemli. Öğünlerinizi düzenli aralıklarla alın, aşırıya kaçmayın."
        ));

        recommendations.put("Akrep", Map.of(
            "foods", "Detoks etkili gıdalar, yeşil çay, sarımsak, soğan, antioksidan açısından zengin besinler",
            "avoid", "Aşırı alkol ve sigara kullanımından kaçının, vücudunuzu temiz tutun",
            "tip", "Vücudunuzun doğal detoks sürecini destekleyin. Su tüketiminizi artırın."
        ));

        recommendations.put("Yay", Map.of(
            "foods", "Karaciğer sağlığını destekleyen gıdalar, egzotik meyveler, baharatlar, çeşitli mutfaklar",
            "avoid", "Aşırı yemek yemekten kaçının, özellikle seyahat ederken dikkatli olun",
            "tip", "Farklı mutfakları denemeyi seversiniz. Karaciğer sağlığınıza dikkat edin."
        ));

        recommendations.put("Oğlak", Map.of(
            "foods", "Kemik sağlığını destekleyen kalsiyum, magnezyum açısından zengin gıdalar, süt ürünleri",
            "avoid", "Aşırı kafein ve asitli içeceklerden kaçının, kemik sağlığınızı koruyun",
            "tip", "Kemik ve diş sağlığınıza özen gösterin. Düzenli beslenme alışkanlığı edinin."
        ));

        recommendations.put("Kova", Map.of(
            "foods", "Dolaşım sistemini destekleyen gıdalar, antioksidanlar, teknolojik gıda takviyeleri",
            "avoid", "Aşırı işlenmiş ve kimyasal katkılı gıdalardan uzak durun",
            "tip", "Kan dolaşımınızı destekleyin. Yenilikçi beslenme tarzlarını deneyebilirsiniz."
        ));

        recommendations.put("Balık", Map.of(
            "foods", "Omega-3 açısından zengin balık, deniz yosunu, spiritüel gıdalar, yumuşak dokulu yemekler",
            "avoid", "Aşırı alkol ve uyuşturucu maddelerden uzak durun, bağımlılık riski yüksek",
            "tip", "Hassas sindirim sisteminiz var. Doğal ve temiz gıdaları tercih edin."
        ));

        return recommendations.getOrDefault(sign, Map.of(
            "foods", "Dengeli beslenme önemli",
            "avoid", "Aşırı tüketimden kaçının",
            "tip", "Sağlıklı yaşam tarzını benimseyin"
        ));
    }

    private Map<String, String> getExerciseRecommendations(String sign) {
        Map<String, Map<String, String>> recommendations = new HashMap<>();

        recommendations.put("Koç", Map.of(
            "exercise", "Yoğun kardiyovasküler egzersizler, koşu, boks, martial arts, sprint",
            "duration", "45-60 dakika, haftada 4-5 gün, yüksek yoğunluk",
            "tip", "Rekabetçi sporları tercih edin, enerjinizi boşaltın. Kısa ve yoğun antrenmanlar ideal."
        ));

        recommendations.put("Boğa", Map.of(
            "exercise", "Yoga, pilates, yürüyüş, ağırlık antrenmanı, bahçıvanlık, doğa yürüyüşü",
            "duration", "30-45 dakika, haftada 3-4 gün, orta yoğunluk",
            "tip", "Sabit rutinler oluşturun, doğada egzersiz yapın. Yavaş ve istikrarlı ilerleme."
        ));

        recommendations.put("İkizler", Map.of(
            "exercise", "Çeşitli aktiviteler, dans, tenis, bisiklet, badminton, grup sporları",
            "duration", "30-40 dakika, çeşitli aktiviteler, haftada 4-5 gün",
            "tip", "Sıkılmamak için aktiviteleri değiştirin. Sosyal sporları tercih edin."
        ));

        recommendations.put("Yengeç", Map.of(
            "exercise", "Yüzme, su sporları, ev egzersizleri, yoga, hafif aerobik",
            "duration", "30-45 dakika, haftada 3-4 gün, düşük-orta yoğunluk",
            "tip", "Su sporlarını tercih edin. Duygusal durumunuza göre egzersiz yoğunluğunu ayarlayın."
        ));

        recommendations.put("Aslan", Map.of(
            "exercise", "Dans, aerobik, sahne sporları, golf, tenis, fitness",
            "duration", "45-60 dakika, haftada 4-5 gün, orta-yüksek yoğunluk",
            "tip", "Gösterişli ve eğlenceli sporları seçin. Kalp sağlığınıza odaklanın."
        ));

        recommendations.put("Başak", Map.of(
            "exercise", "Pilates, yoga, yürüyüş, hafif koşu, sistematik antrenman programları",
            "duration", "30-45 dakika, haftada 4-6 gün, düzenli program",
            "tip", "Detaylı antrenman planları yapın. Sindirim sisteminizi destekleyen egzersizler."
        ));

        recommendations.put("Terazi", Map.of(
            "exercise", "Dans, yoga, estetik sporlar, partner egzersizleri, pilates",
            "duration", "30-45 dakika, haftada 3-4 gün, dengeli program",
            "tip", "Güzel ve uyumlu hareketleri tercih edin. Partner ile egzersiz yapmayı deneyin."
        ));

        recommendations.put("Akrep", Map.of(
            "exercise", "Yoğun antrenmanlar, ağırlık kaldırma, martial arts, dağcılık, ekstrem sporlar",
            "duration", "45-75 dakika, haftada 4-5 gün, yüksek yoğunluk",
            "tip", "Zorlu ve dönüştürücü egzersizleri seçin. Zihinsel odaklanma gerektiren sporlar."
        ));

        recommendations.put("Yay", Map.of(
            "exercise", "Koşu, bisiklet, at binme, okçuluk, outdoor sporlar, trekking",
            "duration", "45-60 dakika, haftada 4-5 gün, değişken yoğunluk",
            "tip", "Açık havada ve özgürce hareket edebileceğiniz sporları tercih edin."
        ));

        recommendations.put("Oğlak", Map.of(
            "exercise", "Ağırlık antrenmanı, dağcılık, sistematik fitness, dayanıklılık sporları",
            "duration", "45-60 dakika, haftada 4-5 gün, disiplinli program",
            "tip", "Uzun vadeli hedefler koyun. Kemik ve kas gücünü artıran egzersizler."
        ));

        recommendations.put("Kova", Map.of(
            "exercise", "Grup fitness, teknolojik sporlar, yenilikçi aktiviteler, su sporları",
            "duration", "30-45 dakika, haftada 3-4 gün, değişken program",
            "tip", "Yeni ve farklı sporları deneyin. Kan dolaşımını destekleyen aktiviteler."
        ));

        recommendations.put("Balık", Map.of(
            "exercise", "Yüzme, su aerobiği, yoga, meditasyon, hafif dans, tai chi",
            "duration", "30-45 dakika, haftada 3-4 gün, düşük yoğunluk",
            "tip", "Su sporları ve meditatif hareketleri tercih edin. Ruhsal dengeyi destekleyin."
        ));

        return recommendations.getOrDefault(sign, Map.of(
            "exercise", "Düzenli fiziksel aktivite",
            "duration", "Haftada en az 3 gün, 30 dakika",
            "tip", "Sevdiğiniz aktiviteleri seçin"
        ));
    }

    private Map<String, String> getRisingSignInfo(String risingSign) {
        Map<String, Map<String, String>> risingSignData = new HashMap<>();

        risingSignData.put("Koç", Map.of(
            "personality", "Enerjik ve dinamik bir ilk izlenim bırakırsınız. İnsanlar sizi cesur ve girişken olarak görür.",
            "chartEffect", "Koç yükselen, doğum anınızda doğu ufkunda Koç burcunun bulunduğunu gösterir.",
            "relationships", "İlk tanışmalarda çok etkileyici ve çekicisiniz. Doğal liderlik özellikleriniz partnerleri cezbeder.",
            "career", "İş hayatında girişimci ve kararlı görünürsünüz. Yöneticilik pozisyonları size yakışır.",
            "style", "Cesur renkler ve sportif tarzı tercih edersiniz. Rahat ama şık giyinmeyi seversiniz."
        ));

        risingSignData.put("Boğa", Map.of(
            "personality", "Sakin, güvenilir ve kararlı bir izlenim bırakırsınız. İnsanlar size güven duyar.",
            "chartEffect", "Boğa yükselen, pratik ve gerçekçi yaklaşımınızı vurgular. Maddi güvenlik önemlidir.",
            "relationships", "İlişkilerde güvenilir ve sadık görünürsünüz. Partnerinize güven ve istikrar sağlarsınız.",
            "career", "İş hayatında sabırlı yaklaşımınız takdir edilir. Finans ve sanat alanlarında başarılısınız.",
            "style", "Klasik ve kaliteli parçaları tercih edersiniz. Doğal güzelliği vurgulayan tarzınız var."
        ));

        risingSignData.put("İkizler", Map.of(
            "personality", "Zeki, konuşkan ve meraklı bir izlenim bırakırsınız. İnsanlar sizi eğlenceli bulur.",
            "chartEffect", "İkizler yükselen, çok yönlü kişiliğinizi ve iletişim becerilerinizi ön plana çıkarır.",
            "relationships", "İlişkilerde entelektüel bağlantı kurmayı seversiniz. Konuşmak sizin için önemli.",
            "career", "İletişim, yazarlık, eğitim alanlarında başarılısınız. Çok görevli çalışma tarzınız vardır.",
            "style", "Çeşitli ve değişken tarzınız var. Trend olan parçaları takip etmeyi seversiniz."
        ));

        risingSignData.put("Yengeç", Map.of(
            "personality", "Duygusal, koruyucu ve sezgisel bir izlenim bırakırsınız. İnsanlar size güven duyar.",
            "chartEffect", "Yengeç yükselen, duygusal derinliğinizi ve aile bağlarınızın önemini vurgular.",
            "relationships", "İlişkilerde çok koruyucu ve şefkatlisiniz. Duygusal bağ kurma konusunda yetenekli.",
            "career", "Bakım, eğitim, psikoloji gibi alanlarda başarılısınız. İnsanlarla çalışmayı seversiniz.",
            "style", "Yumuşak renkler ve rahat kıyafetleri tercih edersiniz. Nostaljik parçalar hoşunuza gider."
        ));

        risingSignData.put("Aslan", Map.of(
            "personality", "Gururlu, yaratıcı ve çekici bir izlenim bırakırsınız. İnsanlar sizi karizmatik bulur.",
            "chartEffect", "Aslan yükselen, yaratıcılığınızı ve liderlik özelliklerinizi güçlendirir.",
            "relationships", "İlişkilerde cömert ve tutkulu görünürsünüz. Partnerinizi kraliyet gibi hissettirirsiniz.",
            "career", "Sanat, eğlence, yöneticilik alanlarında başarılısınız. Yaratıcı projeler size uygun.",
            "style", "Gösterişli ve lüks parçaları seversiniz. Altın rengi ve parlak aksesuarlar favoriniz."
        ));

        risingSignData.put("Başak", Map.of(
            "personality", "Detaycı, pratik ve mükemmeliyetçi bir izlenim bırakırsınız.",
            "chartEffect", "Başak yükselen, analitik düşünce yapınızı ve hizmet etme isteğinizi vurgular.",
            "relationships", "İlişkilerde destekleyici ve yardımseversiniz. Partnerinizin ihtiyaçlarını fark edersiniz.",
            "career", "Sağlık, hizmet, analiz gerektiren işlerde başarılısınız. Detay odaklı çalışma tarzınız var.",
            "style", "Sade, temiz ve pratik kıyafetleri tercih edersiniz. Kalite sizin için önemli."
        ));

        risingSignData.put("Terazi", Map.of(
            "personality", "Zarif, diplomatik ve uyumlu bir izlenim bırakırsınız. İnsanlar sizi çekici bulur.",
            "chartEffect", "Terazi yükselen, estetik anlayışınızı ve adalet duygunuzu güçlendirir.",
            "relationships", "İlişkilerde uyumlu ve romantik görünürsünüz. Partnerlik sizin için hayati önem taşır.",
            "career", "Sanat, hukuk, diplomasi alanlarında başarılısınız. Estetik anlayışınız işinize yansır.",
            "style", "Şık, zarif ve uyumlu kıyafetleri seversiniz. Pastel renkler favoriniz."
        ));

        risingSignData.put("Akrep", Map.of(
            "personality", "Gizemli, yoğun ve manyetik bir izlenim bırakırsınız. İnsanlar sizi güçlü bulur.",
            "chartEffect", "Akrep yükselen, dönüşüm gücünüzü ve sezgisel yeteneklerinizi vurgular.",
            "relationships", "İlişkilerde tutkulu ve sadık görünürsünüz. Derin duygusal bağlar kurarsınız.",
            "career", "Psikoloji, araştırma, gizli işler alanlarında başarılısınız. Analiz yeteneğiniz var.",
            "style", "Koyu renkler ve güçlü parçaları tercih edersiniz. Gizemli ve çekici tarzınız var."
        ));

        risingSignData.put("Yay", Map.of(
            "personality", "Özgür, iyimser ve maceracı bir izlenim bırakırsınız. İnsanlar sizi eğlenceli bulur.",
            "chartEffect", "Yay yükselen, felsefik bakış açınızı ve öğrenme isteğinizi güçlendirir.",
            "relationships", "İlişkilerde özgür ve macera dolu görünürsünüz. Birlikte keşfetmeyi seversiniz.",
            "career", "Eğitim, seyahat, yayıncılık alanlarında başarılısınız. Uluslararası işler size uygun.",
            "style", "Rahat, sportif ve etnik parçaları seversiniz. Farklı kültürlerden esinlenirsiniz."
        ));

        risingSignData.put("Oğlak", Map.of(
            "personality", "Ciddi, sorumlu ve kararlı bir izlenim bırakırsınız. İnsanlar sizi güvenilir bulur.",
            "chartEffect", "Oğlak yükselen, disiplinli yaklaşımınızı ve başarı odaklılığınızı vurgular.",
            "relationships", "İlişkilerde sadık ve sorumlu görünürsünüz. Uzun vadeli planlar yaparsınız.",
            "career", "Yöneticilik, finans, mühendislik alanlarında başarılısınız. Sistematik çalışırsınız.",
            "style", "Klasik, resmi ve kaliteli parçaları tercih edersiniz. Zamansız şıklık sizin tarzınız."
        ));

        risingSignData.put("Kova", Map.of(
            "personality", "Özgün, yenilikçi ve bağımsız bir izlenim bırakırsınız. İnsanlar sizi farklı bulur.",
            "chartEffect", "Kova yükselen, insancıl yaklaşımınızı ve teknolojik ilginizi vurgular.",
            "relationships", "İlişkilerde arkadaş canlısı ve özgür görünürsünüz. Entelektüel paylaşım önemli.",
            "career", "Teknoloji, sosyal hizmetler, yenilikçi alanlar size uygun. Grup çalışması seversiniz.",
            "style", "Modern, farklı ve teknolojik parçaları seversiniz. Trend yaratıcı tarzınız var."
        ));

        risingSignData.put("Balık", Map.of(
            "personality", "Hassas, sezgisel ve şefkatli bir izlenim bırakırsınız. İnsanlar sizi empatik bulur.",
            "chartEffect", "Balık yükselen, sanatsal yeteneklerinizi ve spiritüel bağlantınızı güçlendirir.",
            "relationships", "İlişkilerde romantik ve fedakar görünürsünüz. Duygusal bağ kurma konusunda yetenekli.",
            "career", "Sanat, müzik, terapi alanlarında başarılısınız. İnsanlara yardım etmeyi seversiniz.",
            "style", "Yumuşak, akışkan ve romantik parçaları tercih edersiniz. Deniz renkleri favoriniz."
        ));

        return risingSignData.getOrDefault(risingSign, Map.of(
            "personality", "Yükselen burcunuz kişiliğinizin dış yansımasını belirler.",
            "chartEffect", "Doğum anınızdaki yükselen burç, hayata bakış açınızı şekillendirir.",
            "relationships", "İlişkilerde ilk izleniminizi etkiler.",
            "career", "Profesyonel hayatta nasıl görüldüğünüzü belirler.",
            "style", "Giyim tarzınızı ve dış görünümünüzü etkiler."
        ));
    }

    /**
     * Doğum verilerinden yükselen burç hesaplama (basitleştirilmiş algoritma)
     */
    private String calculateRisingSignFromBirthData(String birthDate, String birthTime, String birthCity) {
        try {
            // Doğum saatini parse et
            String[] timeParts = birthTime.split(":");
            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);

            // Doğum tarihini parse et
            String[] dateParts = birthDate.split("-");
            int month = Integer.parseInt(dateParts[1]);
            int day = Integer.parseInt(dateParts[2]);

            // Basitleştirilmiş yükselen burç hesaplama
            // Gerçek hesaplama çok karmaşık olduğu için yaklaşık bir algoritma kullanıyoruz

            // Saate göre temel hesaplama (2 saatte bir burç değişir)
            int risingIndex = (hour / 2) % 12;

            // Ayı da hesaba kat (mevsimsel etki)
            risingIndex = (risingIndex + (month - 1)) % 12;

            // Günü de hesaba kat (ince ayar)
            risingIndex = (risingIndex + (day % 12)) % 12;

            // Şehir etkisi (basit coğrafi düzeltme)
            if (birthCity.toLowerCase().contains("istanbul") ||
                birthCity.toLowerCase().contains("ankara") ||
                birthCity.toLowerCase().contains("izmir")) {
                risingIndex = (risingIndex + 1) % 12;
            }

            String[] zodiacSigns = {
                "Koç", "Boğa", "İkizler", "Yengeç",
                "Aslan", "Başak", "Terazi", "Akrep",
                "Yay", "Oğlak", "Kova", "Balık"
            };

            return zodiacSigns[risingIndex];

        } catch (Exception e) {
            // Hata durumunda rastgele bir burç döndür
            String[] zodiacSigns = {
                "Koç", "Boğa", "İkizler", "Yengeç",
                "Aslan", "Başak", "Terazi", "Akrep",
                "Yay", "Oğlak", "Kova", "Balık"
            };
            return zodiacSigns[(int)(Math.random() * 12)];
        }
    }
}

<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Düzenle - AsTracker</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }
        
        .btn-save:hover {
            opacity: 0.9;
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            color: white;
        }
        
        .form-floating label {
            color: #667eea;
        }
        
        .form-floating .form-control:focus ~ label {
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Başlık -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h2><i class="fas fa-user-edit me-2"></i>Profil Düzenle</h2>
                        <p class="text-muted">Kişisel bilgilerinizi güncelleyebilirsiniz</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profil Düzenleme Formu -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-circle me-2"></i>
                            Kişisel Bilgiler
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/profile/update}" method="post" th:object="${user}">
                            <!-- Temel Bilgiler -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="firstName" th:field="*{firstName}" 
                                               placeholder="Ad" required>
                                        <label for="firstName">
                                            <i class="fas fa-user me-2"></i>Ad
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="lastName" th:field="*{lastName}" 
                                               placeholder="Soyad" required>
                                        <label for="lastName">
                                            <i class="fas fa-user me-2"></i>Soyad
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="email" class="form-control" id="email" th:field="*{email}" 
                                               placeholder="E-posta" required>
                                        <label for="email">
                                            <i class="fas fa-envelope me-2"></i>E-posta
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="age" th:field="*{age}" 
                                               placeholder="Yaş" min="10" max="120">
                                        <label for="age">
                                            <i class="fas fa-birthday-cake me-2"></i>Yaş
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="gender" th:field="*{gender}">
                                            <option value="">Seçiniz</option>
                                            <option value="M">Erkek</option>
                                            <option value="F">Kadın</option>
                                        </select>
                                        <label for="gender">
                                            <i class="fas fa-venus-mars me-2"></i>Cinsiyet
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="zodiacSign" th:field="*{zodiacSign}">
                                            <option value="">Seçiniz</option>
                                            <option value="Koç">Koç</option>
                                            <option value="Boğa">Boğa</option>
                                            <option value="İkizler">İkizler</option>
                                            <option value="Yengeç">Yengeç</option>
                                            <option value="Aslan">Aslan</option>
                                            <option value="Başak">Başak</option>
                                            <option value="Terazi">Terazi</option>
                                            <option value="Akrep">Akrep</option>
                                            <option value="Yay">Yay</option>
                                            <option value="Oğlak">Oğlak</option>
                                            <option value="Kova">Kova</option>
                                            <option value="Balık">Balık</option>
                                        </select>
                                        <label for="zodiacSign">
                                            <i class="fas fa-star me-2"></i>Burç
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Fiziksel Bilgiler -->
                            <h6 class="mb-3 text-primary">
                                <i class="fas fa-ruler me-2"></i>Fiziksel Bilgiler
                            </h6>
                            
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="height" th:field="*{height}" 
                                               placeholder="Boy (cm)" step="0.1" min="100" max="250">
                                        <label for="height">
                                            <i class="fas fa-ruler-vertical me-2"></i>Boy (cm)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="waistCircumference" th:field="*{waistCircumference}" 
                                               placeholder="Bel Çevresi (cm)" step="0.1" min="50" max="200">
                                        <label for="waistCircumference">
                                            <i class="fas fa-circle me-2"></i>Bel Çevresi (cm)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="chestCircumference" th:field="*{chestCircumference}" 
                                               placeholder="Göğüs Çevresi (cm)" step="0.1" min="50" max="200">
                                        <label for="chestCircumference">
                                            <i class="fas fa-expand-arrows-alt me-2"></i>Göğüs Çevresi (cm)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="activityLevel" th:field="*{activityLevel}">
                                            <option value="">Seçiniz</option>
                                            <option value="sedanter">Sedanter (Hareketsiz)</option>
                                            <option value="hafif">Hafif Aktif (Haftada 1-3 gün)</option>
                                            <option value="orta">Orta Aktif (Haftada 3-5 gün)</option>
                                            <option value="yoğun">Yoğun Aktif (Haftada 6-7 gün)</option>
                                            <option value="çok_yoğun">Çok Yoğun (Günde 2 kez)</option>
                                        </select>
                                        <label for="activityLevel">
                                            <i class="fas fa-running me-2"></i>Aktivite Seviyesi
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Butonlar -->
                            <div class="text-center">
                                <button type="submit" class="btn-save me-3">
                                    <i class="fas fa-save me-2"></i>Kaydet
                                </button>
                                <a th:href="@{/profile}" class="btn-cancel">
                                    <i class="fas fa-times me-2"></i>İptal
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

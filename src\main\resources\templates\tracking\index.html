<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spor Takip - AsTracker</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Base template ile aynı navbar stilleri */
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
            margin: 0 5px;
            border-radius: 8px;
            padding: 8px 16px !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(253, 126, 20, 0.1);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(45deg, #fd7e14, #ff8c00);
            color: white !important;
        }

        .navbar-brand {
            font-weight: 700;
            background: linear-gradient(45deg, #fd7e14, #ff8c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
        }

        .tracking-card {
            background: rgba(255,255,255,0.95);
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .tracking-card:hover {
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fd7e14, #ff8c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #fd7e14;
            margin-bottom: 5px;
        }

        .bmi-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .bmi-normal { background: linear-gradient(135deg, #28a745, #20c997); }
        .bmi-underweight { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
        .bmi-overweight { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .bmi-obese { background: linear-gradient(135deg, #dc3545, #e83e8c); }

        .quick-add-form {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .form-floating {
            margin-bottom: 15px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #fd7e14;
            box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, #fd7e14, #ff8c00);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .calendar-day {
            aspect-ratio: 1;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .calendar-day:hover {
            border-color: #fd7e14;
            background: rgba(253, 126, 20, 0.1);
        }

        .calendar-day.has-data {
            background: linear-gradient(135deg, #fd7e14, #ff8c00);
            color: white;
            border-color: #fd7e14;
        }

        .calendar-day.today {
            border-color: #fd7e14;
            background: rgba(253, 126, 20, 0.1);
        }

        .tracking-summary {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .progress-bar-custom {
            height: 8px;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-star-and-crescent me-2"></i>
                AsTracker
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/dashboard}">Ana Sayfa</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link active" th:href="@{/tracking}">Spor Takip</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/diet}">Diyet Takip</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/study}">Ders Takip</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/astrology}">Astroloji</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/profile}">Profilim</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/login}">Giriş</a>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/register}">Kayıt</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <span class="navbar-text me-3">
                            <i class="fas fa-user me-1"></i>
                            <span sec:authentication="name">Kullanıcı</span>
                        </span>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <form th:action="@{/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>Çıkış
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="tracking-card">
                    <div class="card-body text-center">
                        <h2 class="mb-3">
                            <i class="fas fa-dumbbell me-2 text-primary"></i>
                            Spor Takip Paneli
                        </h2>
                        <p class="text-muted mb-0" th:text="${currentDate}">Bugün</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- BMI ve Kalori Bilgileri -->
        <div th:if="${bmi != null}" class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-calculator me-2"></i>
            <strong>Hesaplanan Bilgiler:</strong>
            <span th:text="'BMI: ' + ${bmi} + ' (' + ${bmiCategory} + ')'"></span>
            <span th:if="${dailyCalories != null}" th:text="' | Günlük Kalori İhtiyacı: ' + ${dailyCalories} + ' kcal'"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>



        <!-- Quick Add Form -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="quick-add-form">
                    <h5 class="mb-3">
                        <i class="fas fa-plus-circle me-2 text-primary"></i>
                        Hızlı Kayıt Ekle
                    </h5>

                    <form th:action="@{/tracking/add}" method="post" th:object="${newTracking}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="trackingDate" class="form-label">
                                        <i class="fas fa-calendar me-2"></i>Tarih
                                    </label>
                                    <input type="date" class="form-control" id="trackingDate"
                                           name="trackingDate" th:value="${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')}">
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="targetBodyPart" class="form-label">
                                        <i class="fas fa-bullseye me-2"></i>Çalışılacak Bölge
                                    </label>
                                    <select class="form-control" id="targetBodyPart" th:field="*{targetBodyPart}">
                                        <option value="">Seçiniz</option>
                                        <option value="Göğüs">Göğüs</option>
                                        <option value="Sırt">Sırt</option>
                                        <option value="Omuz">Omuz</option>
                                        <option value="Kol">Kol</option>
                                        <option value="Karın">Karın</option>
                                        <option value="Bacak">Bacak</option>
                                        <option value="Kalça">Kalça</option>
                                        <option value="Tüm Vücut">Tüm Vücut</option>
                                        <option value="Kardiyovasküler">Kardiyovasküler</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="waterIntake" class="form-label">
                                        <i class="fas fa-layer-group me-2"></i>Set Sayısı
                                    </label>
                                    <input type="number" class="form-control" id="waterIntake"
                                           th:field="*{waterIntake}" placeholder="Set sayısı" min="0">
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="exerciseDuration" class="form-label">
                                        <i class="fas fa-redo me-2"></i>Tekrar
                                    </label>
                                    <select class="form-control" id="exerciseDuration" th:field="*{exerciseDuration}">
                                        <option value="">Seçiniz</option>
                                        <option value="4">4</option>
                                        <option value="5">5</option>
                                        <option value="6">6</option>
                                        <option value="7">7</option>
                                        <option value="8">8</option>
                                        <option value="9">9</option>
                                        <option value="10">10</option>
                                        <option value="11">11</option>
                                        <option value="12">12</option>
                                        <option value="13">13</option>
                                        <option value="14">14</option>
                                        <option value="15">15</option>
                                        <option value="16">16</option>
                                        <option value="17">17</option>
                                        <option value="18">18</option>
                                        <option value="19">19</option>
                                        <option value="20">20</option>
                                        <option value="tükeniş">Tükeniş</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-dumbbell me-2"></i>Egzersiz Notları
                                    </label>
                                    <textarea class="form-control" id="notes" th:field="*{notes}"
                                              placeholder="Egzersiz notları" style="height: 80px;"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Kaydet
                            </button>
                        </div>
                    </form>

                    <!-- Kayıtları Sil Butonu -->
                    <div class="text-center mt-3">
                        <form th:action="@{/tracking/clear-test-data}" method="post"
                              onsubmit="return confirm('Tüm hızlı kayıtlarınız silinecek! Emin misiniz?')">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash me-2"></i>Kayıtları Sil
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Egzersiz Önerileri -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="tracking-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-dumbbell me-2"></i>
                            Egzersiz Önerileri
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="bodyPartSelect" class="form-label">Çalışmak istediğiniz bölgeyi seçin:</label>
                                <select id="bodyPartSelect" class="form-select" onchange="showExercises()">
                                    <option value="">Bölge seçiniz</option>
                                    <option value="göğüs">Göğüs</option>
                                    <option value="sırt">Sırt</option>
                                    <option value="omuz">Omuz</option>
                                    <option value="kol">Kol</option>
                                    <option value="bacak">Bacak</option>
                                    <option value="karın">Karın</option>
                                    <option value="kalça">Kalça</option>
                                </select>
                            </div>
                        </div>
                        <div id="exerciseRecommendations" class="row" style="display: none;">
                            <!-- Egzersiz önerileri buraya yüklenecek -->
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Mini Stats Row -->
        <div class="row mb-3">
            <!-- BMI Card -->
            <div class="col-md-4 mb-2" th:if="${bmi != null}">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7)); backdrop-filter: blur(10px);">
                    <div class="card-body text-center py-2">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-calculator text-primary me-2"></i>
                            <div>
                                <small class="text-muted d-block">BMI</small>
                                <strong th:text="${bmi}">0</strong>
                                <small th:text="${bmiCategory}" class="text-muted ms-1">Normal</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Günlük Kalori Card -->
            <div class="col-md-4 mb-2" th:if="${dailyCalories != null}">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7)); backdrop-filter: blur(10px);">
                    <div class="card-body text-center py-2">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-fire text-danger me-2"></i>
                            <div>
                                <small class="text-muted d-block">Günlük Kalori</small>
                                <strong th:text="${#numbers.formatInteger(dailyCalories, 0)}">0</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bu Ay Kayıt -->
            <div class="col-md-4 mb-2">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7)); backdrop-filter: blur(10px);">
                    <div class="card-body text-center py-2">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-calendar-check text-success me-2"></i>
                            <div>
                                <small class="text-muted d-block">Bu Ay Kayıt</small>
                                <strong th:text="${#lists.size(monthlyTrackings)}">0</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Trackings -->
        <div class="row" th:if="${!#lists.isEmpty(monthlyTrackings)}">
            <div class="col-12">
                <div class="tracking-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            Son Kayıtlar
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tarih</th>
                                        <th>Vücut Bölgesi</th>
                                        <th>Set Sayısı</th>
                                        <th>Tekrar</th>
                                        <th>Egzersiz Notları</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="tracking, trackingStat : ${monthlyTrackings}">
                                        <td th:text="${#temporals.format(tracking.trackingDate, 'dd/MM/yyyy')}">01/01/2024</td>
                                        <td>
                                            <span th:if="${tracking.targetBodyPart != null and !#strings.isEmpty(tracking.targetBodyPart)}"
                                                  th:text="${tracking.targetBodyPart}">-</span>
                                            <span th:unless="${tracking.targetBodyPart != null and !#strings.isEmpty(tracking.targetBodyPart)}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${tracking.waterIntake != null}"
                                                  th:text="${tracking.waterIntake}">-</span>
                                            <span th:unless="${tracking.waterIntake != null}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${tracking.exerciseDuration != null}"
                                                  th:text="${tracking.exerciseDuration}">-</span>
                                            <span th:unless="${tracking.exerciseDuration != null}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${tracking.notes != null and !#strings.isEmpty(tracking.notes)}"
                                                  th:text="${#strings.abbreviate(tracking.notes, 50)}">-</span>
                                            <span th:unless="${tracking.notes != null and !#strings.isEmpty(tracking.notes)}">-</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div class="row" th:if="${#lists.isEmpty(monthlyTrackings)}">
            <div class="col-12">
                <div class="tracking-card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Henüz kayıt bulunmuyor</h4>
                        <p class="text-muted">İlk kaydınızı eklemek için yukarıdaki formu kullanın.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-focus on target body part field
        document.getElementById('targetBodyPart').focus();

        // Form validation - sadece hızlı kayıt formu için
        document.querySelector('form[action*="/tracking/add"]').addEventListener('submit', function(e) {
            const targetBodyPart = document.getElementById('targetBodyPart').value;
            const waterIntake = document.getElementById('waterIntake').value;
            const exerciseDuration = document.getElementById('exerciseDuration').value;
            const notes = document.getElementById('notes').value;

            // En az bir alan dolu olmalı
            if (!targetBodyPart && !waterIntake && !exerciseDuration && !notes.trim()) {
                e.preventDefault();
                alert('Lütfen en az bir alan doldurun.');
            }
        });

        // Egzersiz önerileri
        const exerciseData = {
            'göğüs': [
                {
                    name: 'Şınav',
                    description: 'Yere yüzüstü uzanın, ellerinizi omuz genişliğinde açın. Vücudunuzu düz tutarak yukarı kaldırın ve kontrollü şekilde indirin.',
                    target: 'Göğüs kasları, triceps, ön deltoid',
                    tips: 'Başlangıçta dizlerinizi yere değdirerek yapabilirsiniz. Nefes verirken yukarı çıkın.'
                },
                {
                    name: 'Bench Press',
                    description: 'Sırt üstü yatarak barı göğsünüze kontrollü şekilde indirin, sonra güçlü bir hareketle yukarı itin.',
                    target: 'Göğüs kasları, triceps, ön deltoid',
                    tips: 'Omuz bıçaklarınızı sıkın ve ayaklarınızı yere sağlam basın. Ağırlığı kontrol etmeyi unutmayın.'
                },
                {
                    name: 'Dips',
                    description: 'Paralel barlar arasında kollarınızla asılın, vücudunuzu yukarı aşağı hareket ettirin.',
                    target: 'Alt göğüs, triceps',
                    tips: 'Öne eğilerek göğüs kaslarını daha çok çalıştırabilirsiniz. Başlangıçta yardımcı bantla yapın.'
                }
            ],
            'sırt': [
                {
                    name: 'Pull-up',
                    description: 'Bardan asılarak vücudunuzu yukarı çekin. Çeneyi barın üzerine getirmeye çalışın.',
                    target: 'Latissimus dorsi, rhomboid, biceps',
                    tips: 'Başlangıçta yardımcı bantla yapın. Omuz bıçaklarınızı aşağı çekerek başlayın.'
                },
                {
                    name: 'Bent-over Row',
                    description: 'Öne eğilerek ağırlığı göğsünüze doğru çekin. Sırtınızı düz tutun.',
                    target: 'Orta sırt, rhomboid, rear deltoid',
                    tips: 'Dizlerinizi hafif bükerek durun. Dirseklerinizi vücudunuza yakın tutun.'
                },
                {
                    name: 'Lat Pulldown',
                    description: 'Makine ile yukarıdan aşağı çekme hareketi. Barı göğsünüze doğru çekin.',
                    target: 'Latissimus dorsi, biceps',
                    tips: 'Geriye doğru hafif eğilerek çekin. Omuz bıçaklarınızı birbirine yaklaştırın.'
                }
            ],
            'omuz': [
                {
                    name: 'Shoulder Press',
                    description: 'Ağırlığı omuz hizasından başınızın üzerine itin. Kollarınızı tam açın.',
                    target: 'Deltoid kasları, triceps',
                    tips: 'Core kaslarınızı sıkın ve sırtınızı düz tutun. Ağırlığı kontrollü hareket ettirin.'
                },
                {
                    name: 'Lateral Raise',
                    description: 'Kollarınızı yanlara doğru omuz hizasına kadar kaldırın.',
                    target: 'Yan deltoid',
                    tips: 'Dirseklerinizi hafif bükerek yapın. Ağırlığı yavaş yavaş indirin.'
                },
                {
                    name: 'Front Raise',
                    description: 'Ağırlığı öne doğru omuz hizasına kadar kaldırın.',
                    target: 'Ön deltoid',
                    tips: 'Vücudunuzu sallamayın. Hareket sadece omuzdan gelmelidir.'
                }
            ],
            'kol': [
                {
                    name: 'Bicep Curl',
                    description: 'Ağırlığı dirsekten bükerek yukarı kaldırın. Üst kollarınızı sabit tutun.',
                    target: 'Biceps',
                    tips: 'Dirseklerinizi vücudunuza yakın tutun. Ağırlığı yavaş yavaş indirin.'
                },
                {
                    name: 'Tricep Extension',
                    description: 'Ağırlığı başınızın arkasından yukarı itin. Dirseklerinizi sabit tutun.',
                    target: 'Triceps',
                    tips: 'Üst kollarınızı hareket ettirmeyin. Sadece önkol hareket etmelidir.'
                },
                {
                    name: 'Hammer Curl',
                    description: 'Çekiç tutuşu ile ağırlığı yukarı kaldırın. Avuç içleri birbirine bakmalı.',
                    target: 'Biceps, önkol kasları',
                    tips: 'Normal curl\'den daha ağır ağırlık kullanabilirsiniz. Kontrollü hareket edin.'
                }
            ],
            'bacak': [
                {
                    name: 'Squat',
                    description: 'Ayakları omuz genişliğinde açarak çömelin. Kalçanızı geriye doğru itin.',
                    target: 'Quadriceps, glutes, hamstring',
                    tips: 'Dizleriniz ayak parmaklarınızı geçmemeli. Ağırlığınızı topuklarınızda tutun.'
                },
                {
                    name: 'Lunge',
                    description: 'Bir ayağı öne atarak çömelin. Her iki diziniz 90 derece olmalı.',
                    target: 'Quadriceps, glutes, hamstring',
                    tips: 'Üst vücudunuzu dik tutun. Ön diziniz ayak bileğinizin üzerinde olmalı.'
                },
                {
                    name: 'Calf Raise',
                    description: 'Ayak parmaklarınızın ucuna kalkın. En üst noktada 1 saniye bekleyin.',
                    target: 'Gastrocnemius, soleus',
                    tips: 'Yavaş ve kontrollü hareket edin. Tam hareket açıklığını kullanın.'
                }
            ],
            'karın': [
                {
                    name: 'Plank',
                    description: 'Şınav pozisyonunda durarak vücudunuzu düz bir çizgi halinde tutun.',
                    target: 'Core kasları, karın kasları',
                    tips: 'Kalçanızı yukarı kaldırmayın. Nefes almayı unutmayın.'
                },
                {
                    name: 'Crunch',
                    description: 'Sırt üstü yatarak omuz bıçaklarınızı yerden kaldırın.',
                    target: 'Üst karın kasları',
                    tips: 'Boyununuzu çekmeyin. Hareket karın kaslarından gelmelidir.'
                },
                {
                    name: 'Russian Twist',
                    description: 'Oturarak ayaklarınızı yerden kaldırın ve gövdenizi sağa sola çevirin.',
                    target: 'Yan karın kasları, core',
                    tips: 'Sırtınızı düz tutun. Hareket kontrollü olmalıdır.'
                }
            ],
            'kalça': [
                {
                    name: 'Hip Thrust',
                    description: 'Sırtınızı banka yaslayarak kalçanızı yukarı itin.',
                    target: 'Glutes, hamstring',
                    tips: 'En üst noktada kalça kaslarınızı sıkın. Dizleriniz 90 derece olmalı.'
                },
                {
                    name: 'Glute Bridge',
                    description: 'Yerde sırt üstü yatarak kalçanızı yukarı kaldırın.',
                    target: 'Glutes, core',
                    tips: 'Omuz bıçaklarınızdan destek alın. Kalça kaslarınızı sıkın.'
                },
                {
                    name: 'Side Plank',
                    description: 'Yan yatarak vücudunuzu düz bir çizgi halinde tutun.',
                    target: 'Yan core, glutes',
                    tips: 'Kalçanızı yukarı kaldırın. Vücudunuz düz olmalıdır.'
                }
            ]
        };

        function showExercises() {
            const selectedBodyPart = document.getElementById('bodyPartSelect').value;
            const container = document.getElementById('exerciseRecommendations');

            if (!selectedBodyPart) {
                container.style.display = 'none';
                return;
            }

            const exercises = exerciseData[selectedBodyPart];
            let html = '';

            exercises.forEach(exercise => {
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card h-100" style="background: rgba(255,255,255,0.9); border-radius: 10px; border-left: 4px solid #667eea;">
                            <div class="card-body">
                                <h6 class="card-title text-primary">
                                    <i class="fas fa-dumbbell me-2"></i>${exercise.name}
                                </h6>
                                <div class="mb-3">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-play-circle me-1"></i>Nasıl Yapılır:
                                    </h6>
                                    <p class="card-text small">${exercise.description}</p>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-bullseye me-1"></i>
                                        <strong>Hedef Kaslar:</strong> ${exercise.target}
                                    </small>
                                </div>
                                ${exercise.tips ? `
                                <div class="mt-2">
                                    <small class="text-info">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>İpucu:</strong> ${exercise.tips}
                                    </small>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            container.style.display = 'block';
        }
    </script>
</body>
</html>

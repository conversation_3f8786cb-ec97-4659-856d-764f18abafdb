package Java_Proje.WebProje.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "diet_tracking")
public class DietTracking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull(message = "Tarih boş olamaz")
    @Column(name = "diet_date", nullable = false)
    private LocalDate dietDate;

    @Column(name = "meal_type")
    private String mealType; // Öğün türü (Kahvaltı, Öğle, Akşam, Ara Öğün)

    @Column(name = "food_name")
    private String foodName; // Yemek adı

    @Column(name = "portion_size")
    private String portionSize; // Porsiyon boyutu

    @DecimalMin(value = "0.0", message = "Kalori 0'dan büyük olmalıdır")
    @Column(name = "calories", precision = 7, scale = 2)
    private BigDecimal calories; // Kalori

    @DecimalMin(value = "0.0", message = "Protein 0'dan büyük olmalıdır")
    @Column(name = "protein", precision = 5, scale = 2)
    private BigDecimal protein; // Protein (gram)

    @DecimalMin(value = "0.0", message = "Karbonhidrat 0'dan büyük olmalıdır")
    @Column(name = "carbohydrates", precision = 5, scale = 2)
    private BigDecimal carbohydrates; // Karbonhidrat (gram)

    @DecimalMin(value = "0.0", message = "Yağ 0'dan büyük olmalıdır")
    @Column(name = "fat", precision = 5, scale = 2)
    private BigDecimal fat; // Yağ (gram)

    @Column(name = "water_intake")
    private Integer waterIntake; // Su tüketimi (bardak)

    @Column(name = "meal_time")
    private String mealTime; // Öğün saati

    @Column(name = "diet_notes", columnDefinition = "TEXT")
    private String dietNotes; // Diyet notları

    @Column(name = "satisfaction_level")
    private Integer satisfactionLevel; // Memnuniyet seviyesi (1-10)

    @Column(name = "hunger_level_before")
    private Integer hungerLevelBefore; // Önceki açlık seviyesi (1-10)

    @Column(name = "hunger_level_after")
    private Integer hungerLevelAfter; // Sonraki açlık seviyesi (1-10)

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public DietTracking() {}

    public DietTracking(User user, LocalDate dietDate) {
        this.user = user;
        this.dietDate = dietDate;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDate getDietDate() {
        return dietDate;
    }

    public void setDietDate(LocalDate dietDate) {
        this.dietDate = dietDate;
    }

    public String getMealType() {
        return mealType;
    }

    public void setMealType(String mealType) {
        this.mealType = mealType;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public String getPortionSize() {
        return portionSize;
    }

    public void setPortionSize(String portionSize) {
        this.portionSize = portionSize;
    }

    public BigDecimal getCalories() {
        return calories;
    }

    public void setCalories(BigDecimal calories) {
        this.calories = calories;
    }

    public BigDecimal getProtein() {
        return protein;
    }

    public void setProtein(BigDecimal protein) {
        this.protein = protein;
    }

    public BigDecimal getCarbohydrates() {
        return carbohydrates;
    }

    public void setCarbohydrates(BigDecimal carbohydrates) {
        this.carbohydrates = carbohydrates;
    }

    public BigDecimal getFat() {
        return fat;
    }

    public void setFat(BigDecimal fat) {
        this.fat = fat;
    }

    public Integer getWaterIntake() {
        return waterIntake;
    }

    public void setWaterIntake(Integer waterIntake) {
        this.waterIntake = waterIntake;
    }

    public String getMealTime() {
        return mealTime;
    }

    public void setMealTime(String mealTime) {
        this.mealTime = mealTime;
    }

    public String getDietNotes() {
        return dietNotes;
    }

    public void setDietNotes(String dietNotes) {
        this.dietNotes = dietNotes;
    }

    public Integer getSatisfactionLevel() {
        return satisfactionLevel;
    }

    public void setSatisfactionLevel(Integer satisfactionLevel) {
        this.satisfactionLevel = satisfactionLevel;
    }

    public Integer getHungerLevelBefore() {
        return hungerLevelBefore;
    }

    public void setHungerLevelBefore(Integer hungerLevelBefore) {
        this.hungerLevelBefore = hungerLevelBefore;
    }

    public Integer getHungerLevelAfter() {
        return hungerLevelAfter;
    }

    public void setHungerLevelAfter(Integer hungerLevelAfter) {
        this.hungerLevelAfter = hungerLevelAfter;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}

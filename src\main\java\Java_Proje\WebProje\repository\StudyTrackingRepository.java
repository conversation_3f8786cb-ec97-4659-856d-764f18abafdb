package Java_Proje.WebProje.repository;

import Java_Proje.WebProje.model.StudyTracking;
import Java_Proje.WebProje.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StudyTrackingRepository extends JpaRepository<StudyTracking, Long> {

    /**
     * Kullanıcıya ait tüm ders takip kayıtlarını tarihe göre sıralı getirir
     */
    List<StudyTracking> findByUserOrderByStudyDateDesc(User user);

    /**
     * Kullanıcıya ait belirli tarih aralığındaki ders kayıtlarını getirir
     */
    @Query("SELECT st FROM StudyTracking st WHERE st.user = :user AND st.studyDate BETWEEN :startDate AND :endDate ORDER BY st.studyDate DESC")
    List<StudyTracking> findByUserAndDateRange(@Param("user") User user,
                                              @Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir tarihteki tüm ders kayıtlarını getirir
     */
    List<StudyTracking> findByUserAndStudyDate(User user, LocalDate studyDate);

    /**
     * Kullanıcının son N gün ders kayıtlarını getirir
     */
    @Query("SELECT st FROM StudyTracking st WHERE st.user = :user AND st.studyDate >= :fromDate ORDER BY st.studyDate DESC")
    List<StudyTracking> findRecentStudyTrackings(@Param("user") User user, @Param("fromDate") LocalDate fromDate);

    /**
     * Kullanıcının toplam ders kayıt sayısını döner
     */
    long countByUser(User user);

    /**
     * Kullanıcının bu ayki ders kayıt sayısını döner
     */
    @Query("SELECT COUNT(st) FROM StudyTracking st WHERE st.user = :user AND MONTH(st.studyDate) = MONTH(CURRENT_DATE) AND YEAR(st.studyDate) = YEAR(CURRENT_DATE)")
    long countThisMonthByUser(@Param("user") User user);

    /**
     * Kullanıcının son 10 ders kaydını getirir
     */
    @Query(value = "SELECT * FROM study_tracking WHERE user_id = :#{#user.id} ORDER BY study_date DESC LIMIT 10", nativeQuery = true)
    List<StudyTracking> findTop10ByUserOrderByStudyDateDesc(@Param("user") User user);

    /**
     * Kullanıcının belirli bir tarihteki ders kayıt sayısını döner
     */
    @Query("SELECT COUNT(st) FROM StudyTracking st WHERE st.user = :user AND st.studyDate = :date")
    long countByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının belirli tarih aralığındaki her gün için ders kayıt sayılarını döner
     */
    @Query("SELECT st.studyDate, COUNT(st) FROM StudyTracking st WHERE st.user = :user AND st.studyDate BETWEEN :startDate AND :endDate GROUP BY st.studyDate ORDER BY st.studyDate")
    List<Object[]> countByUserAndDateRange(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir yıl ve aydaki tüm ders kayıtlarını getirir
     */
    @Query("SELECT st FROM StudyTracking st WHERE st.user = :user AND YEAR(st.studyDate) = :year AND MONTH(st.studyDate) = :month ORDER BY st.studyDate DESC")
    List<StudyTracking> findByUserAndYearMonth(@Param("user") User user, @Param("year") int year, @Param("month") int month);

    /**
     * Kullanıcının ders türüne göre kayıtlarını getirir
     */
    @Query("SELECT st FROM StudyTracking st WHERE st.user = :user AND st.studyType = :studyType ORDER BY st.studyDate DESC")
    List<StudyTracking> findByUserAndStudyType(@Param("user") User user, @Param("studyType") String studyType);

    /**
     * Kullanıcının belirli bir derse ait kayıtlarını getirir
     */
    @Query("SELECT st FROM StudyTracking st WHERE st.user = :user AND st.subjectName = :subjectName ORDER BY st.studyDate DESC")
    List<StudyTracking> findByUserAndSubjectName(@Param("user") User user, @Param("subjectName") String subjectName);
}

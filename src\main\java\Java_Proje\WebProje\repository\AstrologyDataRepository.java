package Java_Proje.WebProje.repository;

import Java_Proje.WebProje.model.AstrologyData;
import Java_Proje.WebProje.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AstrologyDataRepository extends JpaRepository<AstrologyData, Long> {
    
    /**
     * Kullanıcıya ait tüm astroloji verilerini tarihe göre sıralı getirir
     */
    List<AstrologyData> findByUserOrderByDataDateDesc(User user);
    
    /**
     * Kullanıcıya ait belirli tarih aralığındaki astroloji verilerini getirir
     */
    @Query("SELECT ad FROM AstrologyData ad WHERE ad.user = :user AND ad.dataDate BETWEEN :startDate AND :endDate ORDER BY ad.dataDate DESC")
    List<AstrologyData> findByUserAndDateRange(@Param("user") User user, 
                                               @Param("startDate") LocalDate startDate, 
                                               @Param("endDate") LocalDate endDate);
    
    /**
     * Kullanıcının belirli bir tarihteki astroloji verisini getirir
     */
    Optional<AstrologyData> findByUserAndDataDate(User user, LocalDate dataDate);
    
    /**
     * Kullanıcının son N gün astroloji verilerini getirir
     */
    @Query("SELECT ad FROM AstrologyData ad WHERE ad.user = :user AND ad.dataDate >= :fromDate ORDER BY ad.dataDate DESC")
    List<AstrologyData> findRecentAstrologyData(@Param("user") User user, @Param("fromDate") LocalDate fromDate);
    
    /**
     * Kullanıcının bugünkü astroloji verisini getirir
     */
    @Query("SELECT ad FROM AstrologyData ad WHERE ad.user = :user AND ad.dataDate = CURRENT_DATE")
    Optional<AstrologyData> findTodayAstrologyData(@Param("user") User user);
    
    /**
     * Kullanıcının tarot kartı kayıtlarını getirir
     */
    @Query("SELECT ad FROM AstrologyData ad WHERE ad.user = :user AND ad.tarotCard IS NOT NULL ORDER BY ad.dataDate DESC")
    List<AstrologyData> findTarotReadings(@Param("user") User user);
    
    /**
     * Kullanıcının burç yorumlarını getirir
     */
    @Query("SELECT ad FROM AstrologyData ad WHERE ad.user = :user AND ad.horoscopeReading IS NOT NULL ORDER BY ad.dataDate DESC")
    List<AstrologyData> findHoroscopeReadings(@Param("user") User user);
    
    /**
     * Kullanıcının toplam astroloji kayıt sayısını döner
     */
    long countByUser(User user);
    
    /**
     * Kullanıcının bu ayki astroloji kayıt sayısını döner
     */
    @Query("SELECT COUNT(ad) FROM AstrologyData ad WHERE ad.user = :user AND MONTH(ad.dataDate) = MONTH(CURRENT_DATE) AND YEAR(ad.dataDate) = YEAR(CURRENT_DATE)")
    long countThisMonthByUser(@Param("user") User user);
}

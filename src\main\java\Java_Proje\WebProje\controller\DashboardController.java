package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.StudyTracking;
import Java_Proje.WebProje.model.DietTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.PersonalTrackingRepository;
import Java_Proje.WebProje.repository.StudyTrackingRepository;
import Java_Proje.WebProje.repository.DietTrackingRepository;
import Java_Proje.WebProje.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Controller
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private UserService userService;

    @Autowired
    private PersonalTrackingRepository personalTrackingRepository;

    @Autowired
    private StudyTrackingRepository studyTrackingRepository;

    @Autowired
    private DietTrackingRepository dietTrackingRepository;

    /**
     * Dashboard ana sayfası
     */
    @GetMapping
    public String dashboard(Model model) {
        try {
            // Mevcut kullanıcıyı al
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return "redirect:/login";
            }

            // Model'e kullanıcı bilgilerini ekle
            model.addAttribute("user", currentUser);
            model.addAttribute("currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("dd MMMM yyyy")));

            // Son aktiviteler (gerçek verilerden)
            model.addAttribute("recentActivities", getRecentActivities(currentUser));

            // Kullanıcı istatistikleri (şimdilik mock data)
            model.addAttribute("userStats", getUserStats(currentUser));

            // Takvim verileri
            model.addAttribute("calendarData", getCalendarData(currentUser));

            return "dashboard/index";
        } catch (Exception e) {
            e.printStackTrace();
            model.addAttribute("error", "Dashboard yüklenirken hata oluştu: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Test sayfası
     */
    @GetMapping("/test")
    public String test(Model model) {
        try {
            User currentUser = getCurrentUser();
            model.addAttribute("user", currentUser);
            return "dashboard/test";
        } catch (Exception e) {
            e.printStackTrace();
            return "error";
        }
    }



    /**
     * Ayarlar sayfası
     */
    @GetMapping("/settings")
    public String settings(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        model.addAttribute("user", currentUser);
        return "dashboard/settings";
    }

    /**
     * Belirli bir günün kayıtlarını JSON olarak döner (3 kategori: Spor, Diyet, Ders)
     */
    @GetMapping("/calendar/day")
    @ResponseBody
    public Map<String, Object> getDayRecords(@RequestParam String date) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return Map.of("error", "Kullanıcı bulunamadı");
            }

            LocalDate selectedDate = LocalDate.parse(date);

            // 3 farklı kategoriden kayıtları al
            List<PersonalTracking> sportRecords = personalTrackingRepository.findByUserAndTrackingDate(currentUser, selectedDate);
            List<DietTracking> dietRecords = dietTrackingRepository.findByUserAndDietDate(currentUser, selectedDate);
            List<StudyTracking> studyRecords = studyTrackingRepository.findByUserAndStudyDate(currentUser, selectedDate);

            Map<String, Object> result = new HashMap<>();

            // Spor kayıtları
            List<Map<String, Object>> sportList = new ArrayList<>();
            for (PersonalTracking record : sportRecords) {
                Map<String, Object> recordData = new HashMap<>();
                recordData.put("id", record.getId());
                recordData.put("category", "Spor");

                StringBuilder description = new StringBuilder();
                if (record.getTargetBodyPart() != null && !record.getTargetBodyPart().isEmpty()) {
                    description.append("Hedef: ").append(record.getTargetBodyPart());
                }
                if (record.getExerciseDuration() != null && record.getExerciseDuration() > 0) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Tekrar: ").append(record.getExerciseDuration());
                }
                if (record.getWaterIntake() != null && record.getWaterIntake() > 0) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Set: ").append(record.getWaterIntake());
                }
                if (record.getNotes() != null && !record.getNotes().isEmpty()) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Not: ").append(record.getNotes());
                }
                if (description.length() == 0) {
                    description.append("Spor kaydı");
                }

                recordData.put("description", description.toString());
                sportList.add(recordData);
            }

            // Diyet kayıtları
            List<Map<String, Object>> dietList = new ArrayList<>();
            for (DietTracking record : dietRecords) {
                Map<String, Object> recordData = new HashMap<>();
                recordData.put("id", record.getId());
                recordData.put("category", "Diyet");

                StringBuilder description = new StringBuilder();
                if (record.getMealType() != null && !record.getMealType().isEmpty()) {
                    description.append(record.getMealType()).append(": ");
                }
                if (record.getFoodName() != null && !record.getFoodName().isEmpty()) {
                    description.append(record.getFoodName());
                }
                if (record.getCalories() != null) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Kalori: ").append(record.getCalories());
                }
                if (record.getProtein() != null) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Protein: ").append(record.getProtein()).append("g");
                }
                if (description.length() == 0) {
                    description.append("Diyet kaydı");
                }

                recordData.put("description", description.toString());
                dietList.add(recordData);
            }

            // Ders kayıtları
            List<Map<String, Object>> studyList = new ArrayList<>();
            for (StudyTracking record : studyRecords) {
                Map<String, Object> recordData = new HashMap<>();
                recordData.put("id", record.getId());
                recordData.put("category", "Ders");

                StringBuilder description = new StringBuilder();
                if (record.getSubjectName() != null && !record.getSubjectName().isEmpty()) {
                    description.append("Ders: ").append(record.getSubjectName());
                }
                if (record.getTopic() != null && !record.getTopic().isEmpty()) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Konu: ").append(record.getTopic());
                }
                if (record.getStudyDuration() != null) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Süre: ").append(record.getStudyDuration()).append(" dk");
                }
                if (record.getStudyType() != null && !record.getStudyType().isEmpty()) {
                    if (description.length() > 0) description.append(" | ");
                    description.append("Tür: ").append(record.getStudyType());
                }
                if (description.length() == 0) {
                    description.append("Ders kaydı");
                }

                recordData.put("description", description.toString());
                studyList.add(recordData);
            }

            result.put("success", true);
            result.put("sportRecords", sportList);
            result.put("dietRecords", dietList);
            result.put("studyRecords", studyList);
            result.put("totalCount", sportList.size() + dietList.size() + studyList.size());

            return result;
        } catch (Exception e) {
            return Map.of("error", "Kayıtlar yüklenirken hata oluştu: " + e.getMessage());
        }
    }

    /**
     * Mevcut oturum açmış kullanıcıyı getirir
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userService.findByUsername(username);
        return userOptional.orElse(null);
    }

    /**
     * Son aktiviteleri getirir (gerçek verilerden) - Sadece son 3 kayıt
     */
    private List<Map<String, Object>> getRecentActivities(User user) {
        List<Map<String, Object>> activities = new ArrayList<>();

        // Sadece hızlı kayıt verilerini al (kilo olmayan kayıtlar)
        List<PersonalTracking> recentTrackings = personalTrackingRepository.findQuickTrackingsByUser(user);

        // Her kayıt için aktivite oluştur
        for (PersonalTracking tracking : recentTrackings) {

            Map<String, Object> activity = new HashMap<>();

            // Detaylı aktivite bilgileri oluştur
            StringBuilder description = new StringBuilder();
            String activityType = "Genel Takip";

            // Kilo bilgisi varsa
            if (tracking.getWeight() != null) {
                activityType = "Kilo Takibi";
                description.append("Kilo: ").append(tracking.getWeight()).append(" kg");
            }

            // Hedef bölge bilgisi varsa
            if (tracking.getTargetBodyPart() != null && !tracking.getTargetBodyPart().isEmpty()) {
                if (description.length() > 0) description.append(" | ");
                activityType = "Egzersiz";
                description.append("Hedef: ").append(tracking.getTargetBodyPart());
            }

            // Su tüketimi varsa
            if (tracking.getWaterIntake() != null && tracking.getWaterIntake() > 0) {
                if (description.length() > 0) description.append(" | ");
                description.append("Su: ").append(tracking.getWaterIntake()).append(" bardak");
            }

            // Egzersiz süresi varsa
            if (tracking.getExerciseDuration() != null && tracking.getExerciseDuration() > 0) {
                if (description.length() > 0) description.append(" | ");
                activityType = "Egzersiz";
                description.append("Egzersiz: ").append(tracking.getExerciseDuration()).append(" dk");
            }



            // Egzersiz notları varsa
            if (tracking.getNotes() != null && !tracking.getNotes().isEmpty()) {
                if (description.length() > 0) description.append(" | ");
                description.append("Not: ").append(tracking.getNotes().length() > 30 ?
                    tracking.getNotes().substring(0, 30) + "..." : tracking.getNotes());
            }

            // Eğer hiçbir bilgi yoksa varsayılan mesaj
            if (description.length() == 0) {
                description.append("Günlük veri girişi yapıldı");
            }

            activity.put("type", activityType);
            activity.put("description", description.toString());
            activity.put("date", tracking.getTrackingDate().format(DateTimeFormatter.ofPattern("dd/MM")));
            activities.add(activity);
        }

        // Eğer hiç veri yoksa boş liste döndür (template'te kontrol edilecek)

        return activities;
    }

    /**
     * Kullanıcı istatistiklerini getirir (gerçek verilerden)
     */
    private Map<String, Object> getUserStats(User user) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // Kullanıcının boy bilgisi
            if (user.getHeight() != null) {
                stats.put("height", user.getHeight().toString());
            }

            // Son kilo kaydını al
            Optional<PersonalTracking> latestWeight = personalTrackingRepository.findLatestWeightTracking(user);
            if (latestWeight.isPresent() && latestWeight.get().getWeight() != null) {
                BigDecimal weight = latestWeight.get().getWeight();
                stats.put("weight", weight.toString());

                // BMI hesapla (eğer boy da varsa)
                if (user.getHeight() != null && user.getHeight().compareTo(BigDecimal.ZERO) > 0) {
                    // BMI = kilo / (boy/100)^2
                    BigDecimal heightInMeters = user.getHeight().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    BigDecimal bmi = weight.divide(heightInMeters.multiply(heightInMeters), 1, RoundingMode.HALF_UP);
                    stats.put("bmi", bmi.toString());

                    // BMI kategorisi
                    if (bmi.compareTo(new BigDecimal("18.5")) < 0) {
                        stats.put("bmiCategory", "Zayıf");
                    } else if (bmi.compareTo(new BigDecimal("25")) < 0) {
                        stats.put("bmiCategory", "Normal");
                    } else if (bmi.compareTo(new BigDecimal("30")) < 0) {
                        stats.put("bmiCategory", "Fazla Kilolu");
                    } else {
                        stats.put("bmiCategory", "Obez");
                    }

                    // Günlük kalori hesapla
                    if (user.getAge() != null && user.getGender() != null) {
                        Double dailyCalories = calculateDailyCalories(user, weight);
                        if (dailyCalories != null) {
                            stats.put("dailyCalories", String.valueOf(Math.round(dailyCalories)));
                        }
                    }
                }
            }

            // Vücut ölçüleri
            if (user.getWaistCircumference() != null) {
                stats.put("waist", user.getWaistCircumference().toString());
            }
            if (user.getChestCircumference() != null) {
                stats.put("chest", user.getChestCircumference().toString());
            }

        } catch (Exception e) {
            System.err.println("getUserStats error: " + e.getMessage());
        }

        return stats;
    }

    /**
     * Günlük kalori ihtiyacını hesapla (Harris-Benedict formülü)
     */
    private Double calculateDailyCalories(User user, BigDecimal weight) {
        if (user.getAge() == null || user.getGender() == null || user.getHeight() == null || weight == null) {
            return null;
        }

        double bmr; // Bazal Metabolizma Hızı
        double heightCm = user.getHeight().doubleValue();
        double weightKg = weight.doubleValue();
        int age = user.getAge();

        // Harris-Benedict formülü
        if ("M".equalsIgnoreCase(user.getGender())) {
            // Erkek: BMR = 88.362 + (13.397 × kilo) + (4.799 × boy) - (5.677 × yaş)
            bmr = 88.362 + (13.397 * weightKg) + (4.799 * heightCm) - (5.677 * age);
        } else {
            // Kadın: BMR = 447.593 + (9.247 × kilo) + (3.098 × boy) - (4.330 × yaş)
            bmr = 447.593 + (9.247 * weightKg) + (3.098 * heightCm) - (4.330 * age);
        }

        // Aktivite seviyesi çarpanı
        double activityMultiplier = 1.2; // Varsayılan: Sedanter
        if (user.getActivityLevel() != null) {
            switch (user.getActivityLevel().toLowerCase()) {
                case "sedanter":
                    activityMultiplier = 1.2;
                    break;
                case "hafif":
                    activityMultiplier = 1.375;
                    break;
                case "orta":
                    activityMultiplier = 1.55;
                    break;
                case "yoğun":
                    activityMultiplier = 1.725;
                    break;
                case "çok_yoğun":
                    activityMultiplier = 1.9;
                    break;
            }
        }

        return bmr * activityMultiplier;
    }

    /**
     * Takvim için veri hazırlar (3 kategoriden toplam kayıt sayısı)
     */
    private Map<String, Object> getCalendarData(User user) {
        Map<String, Object> calendarData = new HashMap<>();

        try {
            LocalDate now = LocalDate.now();
            LocalDate startDate = now.withDayOfMonth(1); // Bu ayın başı
            LocalDate endDate = now.plusMonths(2).withDayOfMonth(1).minusDays(1); // Gelecek ayın sonu

            // 3 farklı kategoriden kayıt sayılarını al
            List<Object[]> sportCounts = personalTrackingRepository.countByUserAndDateRange(user, startDate, endDate);
            List<Object[]> dietCounts = dietTrackingRepository.countByUserAndDateRange(user, startDate, endDate);
            List<Object[]> studyCounts = studyTrackingRepository.countByUserAndDateRange(user, startDate, endDate);

            // Tarih -> toplam kayıt sayısı map'i oluştur
            Map<String, Integer> dateCountMap = new HashMap<>();

            // Spor kayıtları
            for (Object[] row : sportCounts) {
                LocalDate date = (LocalDate) row[0];
                Long count = (Long) row[1];
                dateCountMap.put(date.toString(), dateCountMap.getOrDefault(date.toString(), 0) + count.intValue());
            }

            // Diyet kayıtları
            for (Object[] row : dietCounts) {
                LocalDate date = (LocalDate) row[0];
                Long count = (Long) row[1];
                dateCountMap.put(date.toString(), dateCountMap.getOrDefault(date.toString(), 0) + count.intValue());
            }

            // Ders kayıtları
            for (Object[] row : studyCounts) {
                LocalDate date = (LocalDate) row[0];
                Long count = (Long) row[1];
                dateCountMap.put(date.toString(), dateCountMap.getOrDefault(date.toString(), 0) + count.intValue());
            }

            calendarData.put("currentYear", now.getYear());
            calendarData.put("currentMonth", now.getMonthValue());
            calendarData.put("recordCounts", dateCountMap);

        } catch (Exception e) {
            System.err.println("getCalendarData error: " + e.getMessage());
            calendarData.put("error", "Takvim verileri yüklenirken hata oluştu");
        }

        return calendarData;
    }
}

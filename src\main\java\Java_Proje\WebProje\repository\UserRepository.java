package Java_Proje.WebProje.repository;

import Java_Proje.WebProje.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Kullanıcı adına göre kullanıcı bulur
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Email adresine göre kullanıcı bulur
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Kullanıcı adı veya email ile kullanıcı bulur
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);
    
    /**
     * Kullanıcı adının var olup olmadığını kontrol eder
     */
    boolean existsByUsername(String username);
    
    /**
     * Email adresinin var olup olmadığını kontrol eder
     */
    boolean existsByEmail(String email);
    
    /**
     * Burç işaretine göre kullanıcıları bulur
     */
    @Query("SELECT u FROM User u WHERE u.zodiacSign = :zodiacSign")
    java.util.List<User> findByZodiacSign(@Param("zodiacSign") String zodiacSign);
    
    /**
     * Aktif kullanıcı sayısını döner
     */
    @Query("SELECT COUNT(u) FROM User u")
    long countActiveUsers();
}

<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ana <PERSON>fa - Astroloji & <PERSON><PERSON></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Navigation için beyaz arka plan */
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .info-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.3);
            height: 100%;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .zodiac-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin: 5px;
        }

        .activity-item {
            background: rgba(255,255,255,0.7);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .stat-item {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin-bottom: 10px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .activity-section {
            min-height: 400px;
        }

        .stats-section {
            min-height: 400px;
        }

        .astro-info {
            background: linear-gradient(135deg, #fd7e14, #ffc107);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        /* Takvim Stilleri */
        .calendar {
            max-width: 100%;
            margin: 0 auto;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
        }

        .calendar-nav-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .calendar-nav-btn:hover {
            background: #5a6fd8;
        }

        .calendar-nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .calendar-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 10px;
        }

        .calendar-day-header {
            background: #667eea;
            color: white;
            padding: 10px 5px;
            text-align: center;
            font-weight: bold;
            font-size: 0.9rem;
            border-radius: 5px;
        }

        .calendar-day {
            background: white;
            border: 1px solid #e9ecef;
            padding: 8px 4px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 5px;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .calendar-day:hover {
            background: #f8f9fa;
            transform: scale(1.05);
        }

        .calendar-day.other-month {
            color: #ccc;
            background: #f8f9fa;
        }

        .calendar-day.today {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .calendar-day.has-records {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            font-weight: bold;
        }

        .calendar-day.has-records:hover {
            background: linear-gradient(45deg, #218838, #1ea080);
        }

        .record-count {
            font-size: 0.7rem;
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 2px 6px;
            margin-top: 2px;
        }

        .calendar-day.today .record-count {
            background: rgba(255,255,255,0.4);
        }

        /* Modal Stilleri */
        .day-records-modal .modal-content {
            border-radius: 15px;
        }

        .record-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .record-type {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .record-description {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-home me-3"></i>
                            Hoş Geldiniz!
                        </h1>
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 th:if="${user}">
                            <span th:text="${user.firstName}">Ad</span>
                            <span th:text="${user.lastName}">Soyad</span>
                        </h3>
                        <h3 th:unless="${user}">Kullanıcı</h3>

                        <p class="text-muted" th:text="${user != null ? user.email : '<EMAIL>'}"><EMAIL></p>

                        <div th:if="${user != null and user.zodiacSign != null}">
                            <span class="zodiac-badge">
                                <i class="fas fa-star me-2"></i>
                                <span th:text="${user.zodiacSign}">Burç</span>
                            </span>
                            <span th:if="${user.risingSign != null}" class="zodiac-badge ms-2">
                                <i class="fas fa-arrow-up me-2"></i>
                                Yükselen: <span th:text="${user.risingSign}">Yükselen</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Astroloji Bilgileri -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="astro-info">
                    <h5><i class="fas fa-star-and-crescent me-2"></i>Astroloji Bilgilerin</h5>
                    <p class="mb-2">Burcun: <strong th:text="${user != null and user.zodiacSign != null ? user.zodiacSign : 'Henüz belirlenmedi'}">Burç</strong></p>
                    <p class="mb-2" th:if="${user != null and user.risingSign != null}">
                        Yükselen: <strong th:text="${user.risingSign}">Yükselen</strong>
                    </p>
                    <p class="mb-0">Astroloji dünyasını keşfet ve kendini daha iyi tanı!</p>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="info-card">
                    <h5><i class="fas fa-calendar-day me-2"></i>Bugün</h5>
                    <p class="mb-2" th:text="${currentDate}">Tarih</p>
                    <p class="mb-0">Yeni bir gün, yeni fırsatlar! Hedeflerine odaklan.</p>
                </div>
            </div>
        </div>

        <!-- Kişisel Takip Takvimi -->
        <div class="row mb-4">
            <div class="col-12 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Kişisel Takip Takvimi
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="calendar-container">
                            <!-- Takvim buraya yüklenecek -->
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Yükleniyor...</span>
                                </div>
                                <p class="mt-2">Takvim yükleniyor...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kişisel Takip Özeti - ULTRA MİNİ KUTUCUK -->
        <div class="row mb-1">
            <div class="col-12">
                <div class="card" style="height: 60px; border: 1px solid #e0e0e0;">
                    <div class="card-body p-1 d-flex align-items-center justify-content-between" style="height: 100%;">
                        <!-- Sol taraf: Başlık -->
                        <div style="font-size: 0.85rem; font-weight: bold; color: #666;">
                            <i class="fas fa-chart-line me-1"></i>Kişisel Takip
                        </div>

                        <!-- Orta: Veriler -->
                        <div th:if="${userStats != null and !userStats.isEmpty()}" class="d-flex gap-3">
                            <div th:if="${user != null and user.height != null}" class="text-center">
                                <div class="text-muted" style="font-size: 0.75rem;">Boy</div>
                                <div class="fw-bold" th:text="${user.height} + 'cm'" style="font-size: 0.85rem;">190cm</div>
                            </div>
                            <div th:if="${userStats.weight}" class="text-center">
                                <div class="text-muted" style="font-size: 0.75rem;">Kilo</div>
                                <div class="fw-bold" th:text="${userStats.weight} + 'kg'" style="font-size: 0.85rem;">90kg</div>
                            </div>
                            <div th:if="${userStats.bmi}" class="text-center">
                                <div class="text-muted" style="font-size: 0.75rem;">BMI</div>
                                <div class="fw-bold" th:text="${userStats.bmi}" style="font-size: 0.85rem;">24.9</div>
                            </div>
                        </div>

                        <!-- Veri yoksa mesaj -->
                        <div th:if="${userStats == null or userStats.isEmpty()}" style="font-size: 0.8rem; color: #999;">
                            Henüz bilgi girilmedi
                        </div>

                        <!-- Sağ taraf: Buton -->
                        <div>
                            <a href="/tracking" class="btn btn-primary" style="font-size: 0.75rem; padding: 3px 8px;">
                                <i class="fas fa-plus"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hızlı Erişim -->
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="/tracking" class="text-decoration-none">
                    <div class="info-card">
                        <i class="fas fa-dumbbell" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <h6>Spor Takip</h6>
                        <small class="text-muted">Egzersiz ve antrenman</small>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/diet" class="text-decoration-none">
                    <div class="info-card">
                        <i class="fas fa-utensils" style="font-size: 2rem; color: #28a745; margin-bottom: 15px;"></i>
                        <h6>Diyet Takip</h6>
                        <small class="text-muted">Beslenme ve kalori</small>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/study" class="text-decoration-none">
                    <div class="info-card">
                        <i class="fas fa-book" style="font-size: 2rem; color: #dc3545; margin-bottom: 15px;"></i>
                        <h6>Ders Takip</h6>
                        <small class="text-muted">Çalışma programı</small>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/astrology" class="text-decoration-none">
                    <div class="info-card">
                        <i class="fas fa-star-and-crescent" style="font-size: 2rem; color: #fd7e14; margin-bottom: 15px;"></i>
                        <h6>Astroloji</h6>
                        <small class="text-muted">Burç yorumları ve tarot</small>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Günlük Kayıtlar Modal -->
    <div class="modal fade day-records-modal" id="dayRecordsModal" tabindex="-1" aria-labelledby="dayRecordsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dayRecordsModalLabel">
                        <i class="fas fa-calendar-day me-2"></i>
                        <span id="selectedDateTitle">Günlük Kayıtlar</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="dayRecordsContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Yükleniyor...</span>
                            </div>
                            <p class="mt-2">Kayıtlar yükleniyor...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                    <a href="/tracking" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Yeni Kayıt Ekle
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- CSRF Meta Tags -->
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        // Takvim verileri (Thymeleaf'ten gelen)
        const calendarData = /*[[${calendarData}]]*/ {};

        document.addEventListener('DOMContentLoaded', function() {
            // Dashboard kartları için animasyon
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Hover efektleri
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Dinamik tarih güncelleme
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // Takvimi başlat
            initCalendar();
        });

        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeString = now.toLocaleDateString('tr-TR', options);

            const dateElements = document.querySelectorAll('[data-current-date]');
            dateElements.forEach(element => {
                element.textContent = dateTimeString;
            });
        }

        // Takvim değişkenleri
        let currentDate = new Date();
        let currentYear = calendarData.currentYear || new Date().getFullYear();
        let currentMonth = calendarData.currentMonth || new Date().getMonth() + 1;
        const recordCounts = calendarData.recordCounts || {};

        function initCalendar() {
            renderCalendar();
        }

        function renderCalendar() {
            const container = document.getElementById('calendar-container');

            const monthNames = [
                'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
                'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
            ];

            const dayNames = ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'];

            let html = `
                <div class="calendar">
                    <div class="calendar-header">
                        <button class="calendar-nav-btn" onclick="previousMonth()" ${currentYear <= new Date().getFullYear() && currentMonth <= new Date().getMonth() + 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="calendar-title">
                            ${monthNames[currentMonth - 1]} ${currentYear}
                        </div>
                        <button class="calendar-nav-btn" onclick="nextMonth()" ${currentYear >= 2030 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="calendar-grid">
            `;

            // Gün başlıkları
            dayNames.forEach(day => {
                html += `<div class="calendar-day-header">${day}</div>`;
            });

            // Ayın ilk günü hangi gün
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            let startDay = firstDay.getDay();
            if (startDay === 0) startDay = 7; // Pazar = 7
            startDay = startDay - 1; // Pazartesi = 0

            // Ayın gün sayısı
            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();

            // Önceki ayın son günleri
            const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
            const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
            const daysInPrevMonth = new Date(prevYear, prevMonth, 0).getDate();

            for (let i = startDay - 1; i >= 0; i--) {
                const day = daysInPrevMonth - i;
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            // Bu ayın günleri
            const today = new Date();
            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const recordCount = recordCounts[dateStr] || 0;

                let classes = 'calendar-day';
                if (today.getFullYear() === currentYear && today.getMonth() + 1 === currentMonth && today.getDate() === day) {
                    classes += ' today';
                }
                if (recordCount > 0) {
                    classes += ' has-records';
                }

                html += `
                    <div class="${classes}" onclick="showDayRecords('${dateStr}')">
                        <div>${day}</div>
                        ${recordCount > 0 ? `<div class="record-count">${recordCount} kayıt</div>` : ''}
                    </div>
                `;
            }

            // Sonraki ayın ilk günleri (42 hücre tamamlamak için)
            const totalCells = 42;
            const usedCells = startDay + daysInMonth;
            const remainingCells = totalCells - usedCells;

            for (let day = 1; day <= remainingCells; day++) {
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            html += `
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        function previousMonth() {
            if (currentMonth === 1) {
                currentMonth = 12;
                currentYear--;
            } else {
                currentMonth--;
            }

            // Geçmiş aylara gitmeyi engelle
            const now = new Date();
            if (currentYear < now.getFullYear() || (currentYear === now.getFullYear() && currentMonth < now.getMonth() + 1)) {
                currentYear = now.getFullYear();
                currentMonth = now.getMonth() + 1;
                return;
            }

            renderCalendar();
        }

        function nextMonth() {
            if (currentMonth === 12) {
                currentMonth = 1;
                currentYear++;
            } else {
                currentMonth++;
            }

            // 2030'dan sonrasına gitmeyi engelle
            if (currentYear > 2030) {
                currentYear = 2030;
                currentMonth = 12;
                return;
            }

            renderCalendar();
        }

        function showDayRecords(dateStr) {
            const modal = new bootstrap.Modal(document.getElementById('dayRecordsModal'));
            const titleElement = document.getElementById('selectedDateTitle');
            const contentElement = document.getElementById('dayRecordsContent');

            // Tarihi formatla
            const date = new Date(dateStr);
            const formattedDate = date.toLocaleDateString('tr-TR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            titleElement.textContent = formattedDate + ' Kayıtları';

            // Loading göster
            contentElement.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Yükleniyor...</span>
                    </div>
                    <p class="mt-2">Kayıtlar yükleniyor...</p>
                </div>
            `;

            modal.show();

            // AJAX ile kayıtları getir
            fetch(`/dashboard/calendar/day?date=${dateStr}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.totalCount > 0) {
                        let html = '';

                        // Spor kayıtları
                        if (data.sportRecords && data.sportRecords.length > 0) {
                            html += `
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-dumbbell me-2"></i>Spor Kayıtları (${data.sportRecords.length})
                                    </h6>
                            `;
                            data.sportRecords.forEach(record => {
                                html += `
                                    <div class="record-item" style="border-left-color: #667eea;">
                                        <div class="record-type">Spor</div>
                                        <div class="record-description">${record.description}</div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        }

                        // Diyet kayıtları
                        if (data.dietRecords && data.dietRecords.length > 0) {
                            html += `
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-utensils me-2"></i>Diyet Kayıtları (${data.dietRecords.length})
                                    </h6>
                            `;
                            data.dietRecords.forEach(record => {
                                html += `
                                    <div class="record-item" style="border-left-color: #28a745;">
                                        <div class="record-type">Diyet</div>
                                        <div class="record-description">${record.description}</div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        }

                        // Ders kayıtları
                        if (data.studyRecords && data.studyRecords.length > 0) {
                            html += `
                                <div class="mb-4">
                                    <h6 class="text-danger mb-3">
                                        <i class="fas fa-book me-2"></i>Ders Kayıtları (${data.studyRecords.length})
                                    </h6>
                            `;
                            data.studyRecords.forEach(record => {
                                html += `
                                    <div class="record-item" style="border-left-color: #dc3545;">
                                        <div class="record-type">Ders</div>
                                        <div class="record-description">${record.description}</div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        }

                        contentElement.innerHTML = html;
                    } else {
                        contentElement.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                Bu tarihte henüz kayıt bulunmuyor.
                                <br><br>
                                <div class="d-flex justify-content-center gap-2 flex-wrap">
                                    <a href="/tracking" class="btn btn-primary btn-sm">
                                        <i class="fas fa-dumbbell me-1"></i>Spor
                                    </a>
                                    <a href="/diet" class="btn btn-success btn-sm">
                                        <i class="fas fa-utensils me-1"></i>Diyet
                                    </a>
                                    <a href="/study" class="btn btn-danger btn-sm">
                                        <i class="fas fa-book me-1"></i>Ders
                                    </a>
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    contentElement.innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Kayıtlar yüklenirken hata oluştu.
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>

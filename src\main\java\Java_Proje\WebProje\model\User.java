package Java_Proje.WebProje.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Kullanıcı adı boş olamaz")
    @Size(min = 3, max = 50, message = "Kullanıcı adı 3-50 karakter arası olmalıdır")
    @Column(unique = true, nullable = false)
    private String username;

    @NotBlank(message = "Email boş olamaz")
    @Email(message = "Geçerli bir email adresi giriniz")
    @Column(unique = true, nullable = false)
    private String email;

    @NotBlank(message = "<PERSON><PERSON><PERSON> boş olamaz")
    @Size(min = 6, message = "<PERSON>ifre en az 6 karakter olmalıdır")
    @Column(nullable = false)
    private String password;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Column(name = "zodiac_sign")
    private String zodiacSign;

    @Column(name = "rising_sign")
    private String risingSign;

    @Column(name = "height", precision = 5, scale = 2)
    private java.math.BigDecimal height; // Boy (cm cinsinden)

    @Column(name = "waist_circumference", precision = 5, scale = 2)
    private java.math.BigDecimal waistCircumference; // Bel çevresi (cm)

    @Column(name = "chest_circumference", precision = 5, scale = 2)
    private java.math.BigDecimal chestCircumference; // Göğüs çevresi (cm)

    @Column(name = "age")
    private Integer age; // Yaş

    @Column(name = "gender")
    private String gender; // Cinsiyet (M/F)

    @Column(name = "activity_level")
    private String activityLevel; // Aktivite seviyesi

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PersonalTracking> personalTrackings = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AstrologyData> astrologyData = new ArrayList<>();

    // Constructors
    public User() {}

    public User(String username, String email, String password) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
        this.zodiacSign = calculateZodiacSign(birthDate);
    }

    public String getZodiacSign() {
        return zodiacSign;
    }

    public void setZodiacSign(String zodiacSign) {
        this.zodiacSign = zodiacSign;
    }

    public String getRisingSign() {
        return risingSign;
    }

    public void setRisingSign(String risingSign) {
        this.risingSign = risingSign;
    }

    public java.math.BigDecimal getHeight() {
        return height;
    }

    public void setHeight(java.math.BigDecimal height) {
        this.height = height;
    }

    public java.math.BigDecimal getWaistCircumference() {
        return waistCircumference;
    }

    public void setWaistCircumference(java.math.BigDecimal waistCircumference) {
        this.waistCircumference = waistCircumference;
    }

    public java.math.BigDecimal getChestCircumference() {
        return chestCircumference;
    }

    public void setChestCircumference(java.math.BigDecimal chestCircumference) {
        this.chestCircumference = chestCircumference;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getActivityLevel() {
        return activityLevel;
    }

    public void setActivityLevel(String activityLevel) {
        this.activityLevel = activityLevel;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<PersonalTracking> getPersonalTrackings() {
        return personalTrackings;
    }

    public void setPersonalTrackings(List<PersonalTracking> personalTrackings) {
        this.personalTrackings = personalTrackings;
    }

    public List<AstrologyData> getAstrologyData() {
        return astrologyData;
    }

    public void setAstrologyData(List<AstrologyData> astrologyData) {
        this.astrologyData = astrologyData;
    }

    // Helper method to calculate zodiac sign
    private String calculateZodiacSign(LocalDate birthDate) {
        if (birthDate == null) return null;

        int month = birthDate.getMonthValue();
        int day = birthDate.getDayOfMonth();

        if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) return "Koç";
        if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) return "Boğa";
        if ((month == 5 && day >= 21) || (month == 6 && day <= 20)) return "İkizler";
        if ((month == 6 && day >= 21) || (month == 7 && day <= 22)) return "Yengeç";
        if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) return "Aslan";
        if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) return "Başak";
        if ((month == 9 && day >= 23) || (month == 10 && day <= 22)) return "Terazi";
        if ((month == 10 && day >= 23) || (month == 11 && day <= 21)) return "Akrep";
        if ((month == 11 && day >= 22) || (month == 12 && day <= 21)) return "Yay";
        if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) return "Oğlak";
        if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) return "Kova";
        if ((month == 2 && day >= 19) || (month == 3 && day <= 20)) return "Balık";

        return null;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}

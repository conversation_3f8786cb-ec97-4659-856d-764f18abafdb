<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Astroloji & <PERSON><PERSON><PERSON><PERSON></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .login-card {
            background: rgba(255,255,255,0.95);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-header .subtitle {
            opacity: 0.9;
            margin-top: 10px;
        }

        .login-body {
            padding: 40px 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            opacity: 0.9;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #dee2e6;
        }

        .divider span {
            background: white;
            padding: 0 20px;
            color: #6c757d;
        }

        .register-link {
            text-align: center;
            margin-top: 20px;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border: none;
            border-radius: 10px;
            margin-bottom: 20px;
        }


    </style>
</head>
<body>


    <div class="container">
        <div class="login-container">
            <div class="card login-card">
                <div class="login-header">
                    <i class="fas fa-star-and-crescent fa-3x mb-3"></i>
                    <h2>Hoş Geldiniz</h2>
                    <p class="subtitle mb-0">Hesabınıza giriş yapın</p>
                </div>

                <div class="login-body">
                    <!-- Error Message -->
                    <div th:if="${error}" class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span th:text="${error}">Hata mesajı</span>
                    </div>

                    <!-- Success Message -->
                    <div th:if="${message}" class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <span th:text="${message}">Başarı mesajı</span>
                    </div>

                    <!-- Login Form -->
                    <form th:action="@{/login}" method="post">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" name="username"
                                   placeholder="Kullanıcı adı veya email" required>
                            <label for="username">
                                <i class="fas fa-user me-2"></i>Kullanıcı Adı veya Email
                            </label>
                        </div>

                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Şifre" required>
                            <label for="password">
                                <i class="fas fa-lock me-2"></i>Şifre
                            </label>
                        </div>



                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                        </button>
                    </form>

                    <div class="divider">
                        <span>veya</span>
                    </div>

                    <div class="register-link">
                        <p class="mb-0">Hesabınız yok mu?
                            <a th:href="@{/register}">Hemen kayıt olun</a>
                        </p>
                    </div>

                    <div class="text-center mt-3">
                        <a th:href="@{/}" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-2"></i>Ana sayfaya dön
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                e.preventDefault();
                alert('Lütfen tüm alanları doldurun.');
            }
        });

        // Auto-focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>

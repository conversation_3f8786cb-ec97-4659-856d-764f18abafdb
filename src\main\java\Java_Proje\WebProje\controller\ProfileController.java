package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.PersonalTrackingRepository;
import Java_Proje.WebProje.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Controller
@RequestMapping("/profile")
public class ProfileController {

    @Autowired
    private UserService userService;

    @Autowired
    private PersonalTrackingRepository personalTrackingRepository;

    /**
     * Profil sayfası
     */
    @GetMapping
    public String profile(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        // Kullanıcının son tracking verilerini al
        Optional<PersonalTracking> latestTracking = personalTrackingRepository.findLatestWeightTracking(currentUser);

        // Kullanıcı istatistiklerini hesapla
        Map<String, Object> userStats = calculateUserStats(currentUser, latestTracking);

        // Sadece hızlı kayıt verilerini al (kilo olmayan kayıtlar)
        List<PersonalTracking> recentTrackings = personalTrackingRepository.findQuickTrackingsByUser(currentUser);

        model.addAttribute("user", currentUser);
        model.addAttribute("userStats", userStats);
        model.addAttribute("recentTrackings", recentTrackings);
        model.addAttribute("latestTracking", latestTracking.orElse(null));

        return "profile/index";
    }

    /**
     * Profil güncelleme sayfası
     */
    @GetMapping("/edit")
    public String editProfile(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        model.addAttribute("user", currentUser);
        return "profile/edit";
    }

    /**
     * Profil güncelleme işlemi
     */
    @PostMapping("/update")
    public String updateProfile(@ModelAttribute User updatedUser,
                              RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            // Güvenlik için ID'yi koruyalım
            updatedUser.setId(currentUser.getId());
            updatedUser.setUsername(currentUser.getUsername());
            updatedUser.setPassword(currentUser.getPassword());

            // Kullanıcıyı güncelle
            userService.updateUser(updatedUser);

            redirectAttributes.addFlashAttribute("success", "Profil başarıyla güncellendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Profil güncellenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/profile";
    }

    /**
     * Şifre değiştirme sayfası
     */
    @GetMapping("/change-password")
    public String changePasswordPage(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        model.addAttribute("user", currentUser);
        return "profile/change-password";
    }

    /**
     * Şifre değiştirme işlemi
     */
    @PostMapping("/change-password")
    public String changePassword(@RequestParam("currentPassword") String currentPassword,
                               @RequestParam("newPassword") String newPassword,
                               @RequestParam("confirmPassword") String confirmPassword,
                               RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            // Şifre doğrulama ve güncelleme
            if (!newPassword.equals(confirmPassword)) {
                redirectAttributes.addFlashAttribute("error", "Yeni şifreler eşleşmiyor!");
                return "redirect:/profile/change-password";
            }

            if (newPassword.length() < 6) {
                redirectAttributes.addFlashAttribute("error", "Şifre en az 6 karakter olmalıdır!");
                return "redirect:/profile/change-password";
            }

            // Şifre güncelleme (UserService'te implement edilmeli)
            userService.changePassword(currentUser, currentPassword, newPassword);

            redirectAttributes.addFlashAttribute("success", "Şifre başarıyla değiştirildi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Şifre değiştirilirken hata oluştu: " + e.getMessage());
            return "redirect:/profile/change-password";
        }

        return "redirect:/profile";
    }

    /**
     * Hesap silme işlemi
     */
    @PostMapping("/delete-account")
    public String deleteAccount(@RequestParam("password") String password,
                               RedirectAttributes redirectAttributes,
                               HttpServletRequest request,
                               HttpServletResponse response) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            // Şifre doğrulaması ile hesabı sil
            userService.deleteUserAccount(currentUser, password);

            // Oturumu sonlandır
            SecurityContextLogoutHandler logoutHandler = new SecurityContextLogoutHandler();
            logoutHandler.logout(request, response, SecurityContextHolder.getContext().getAuthentication());

            redirectAttributes.addFlashAttribute("success", "Hesabınız başarıyla silindi!");
            return "redirect:/login";

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/profile";
        }
    }

    /**
     * Mevcut kullanıcıyı getir
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            Optional<User> userOpt = userService.findByUsername(authentication.getName());
            return userOpt.orElse(null);
        }
        return null;
    }

    /**
     * Kullanıcı istatistiklerini hesapla
     */
    private Map<String, Object> calculateUserStats(User user, Optional<PersonalTracking> latestTracking) {
        Map<String, Object> stats = new HashMap<>();

        // Temel bilgiler
        stats.put("height", user.getHeight() != null ? user.getHeight().toString() : "Henüz kayıt oluşturulmadı");
        stats.put("age", user.getAge() != null ? user.getAge().toString() : "Henüz kayıt oluşturulmadı");
        stats.put("gender", user.getGender() != null ? (user.getGender().equals("M") ? "Erkek" : "Kadın") : "Henüz kayıt oluşturulmadı");
        stats.put("activityLevel", user.getActivityLevel() != null ? user.getActivityLevel() : "Henüz kayıt oluşturulmadı");

        // Vücut ölçüleri
        stats.put("waist", user.getWaistCircumference() != null ? user.getWaistCircumference().toString() : "Henüz kayıt oluşturulmadı");
        stats.put("chest", user.getChestCircumference() != null ? user.getChestCircumference().toString() : "Henüz kayıt oluşturulmadı");

        // Son kilo ve BMI
        if (latestTracking.isPresent() && latestTracking.get().getWeight() != null) {
            BigDecimal weight = latestTracking.get().getWeight();
            stats.put("weight", weight.toString());
            stats.put("lastWeightDate", latestTracking.get().getTrackingDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));

            // BMI hesapla
            if (user.getHeight() != null && user.getHeight().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal heightInMeters = user.getHeight().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                BigDecimal bmi = weight.divide(heightInMeters.multiply(heightInMeters), 1, RoundingMode.HALF_UP);
                stats.put("bmi", bmi.toString());

                // BMI kategorisi
                if (bmi.compareTo(new BigDecimal("18.5")) < 0) {
                    stats.put("bmiCategory", "Zayıf");
                } else if (bmi.compareTo(new BigDecimal("25")) < 0) {
                    stats.put("bmiCategory", "Normal");
                } else if (bmi.compareTo(new BigDecimal("30")) < 0) {
                    stats.put("bmiCategory", "Fazla Kilolu");
                } else {
                    stats.put("bmiCategory", "Obez");
                }

                // Günlük kalori hesapla
                if (user.getAge() != null && user.getGender() != null) {
                    Double dailyCalories = calculateDailyCalories(user, weight);
                    if (dailyCalories != null) {
                        stats.put("dailyCalories", String.valueOf(Math.round(dailyCalories)));
                    }
                }
            } else {
                // Boy yoksa BMI hesaplanamaz
                stats.put("bmi", null);
                stats.put("bmiCategory", null);
            }
        } else {
            stats.put("weight", "Henüz kayıt oluşturulmadı");
            stats.put("lastWeightDate", "Henüz kayıt oluşturulmadı");
            stats.put("bmi", null);
            stats.put("bmiCategory", null);
        }

        // Günlük kalori - sadece gerekli veriler varsa hesapla
        if (user.getHeight() == null || user.getAge() == null || user.getGender() == null ||
            !latestTracking.isPresent() || latestTracking.get().getWeight() == null) {
            stats.put("dailyCalories", null);
        }

        // Astroloji bilgileri
        stats.put("zodiacSign", user.getZodiacSign() != null ? user.getZodiacSign() : "Henüz kayıt oluşturulmadı");
        stats.put("risingSign", user.getRisingSign() != null ? user.getRisingSign() : "Henüz kayıt oluşturulmadı");

        return stats;
    }

    /**
     * Günlük kalori ihtiyacını hesapla (Harris-Benedict formülü)
     */
    private Double calculateDailyCalories(User user, BigDecimal weight) {
        if (user.getAge() == null || user.getGender() == null || user.getHeight() == null || weight == null) {
            return null;
        }

        double bmr; // Bazal Metabolizma Hızı
        double heightCm = user.getHeight().doubleValue();
        double weightKg = weight.doubleValue();
        int age = user.getAge();

        // Harris-Benedict formülü
        if ("M".equalsIgnoreCase(user.getGender())) {
            // Erkek: BMR = 88.362 + (13.397 × kilo) + (4.799 × boy) - (5.677 × yaş)
            bmr = 88.362 + (13.397 * weightKg) + (4.799 * heightCm) - (5.677 * age);
        } else {
            // Kadın: BMR = 447.593 + (9.247 × kilo) + (3.098 × boy) - (4.330 × yaş)
            bmr = 447.593 + (9.247 * weightKg) + (3.098 * heightCm) - (4.330 * age);
        }

        // Aktivite seviyesi çarpanı
        double activityMultiplier = 1.2; // Varsayılan: Sedanter
        if (user.getActivityLevel() != null) {
            switch (user.getActivityLevel().toLowerCase()) {
                case "sedanter":
                    activityMultiplier = 1.2;
                    break;
                case "hafif":
                    activityMultiplier = 1.375;
                    break;
                case "orta":
                    activityMultiplier = 1.55;
                    break;
                case "yoğun":
                    activityMultiplier = 1.725;
                    break;
                case "çok_yoğun":
                    activityMultiplier = 1.9;
                    break;
            }
        }

        return bmr * activityMultiplier;
    }
}

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astroloji ve Kişisel Takip Uygulaması Raporu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }
        h3 {
            color: #34495e;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        ul {
            list-style-type: none;
            padding-left: 20px;
        }
        li {
            margin: 10px 0;
            position: relative;
        }
        li:before {
            content: "•";
            color: #3498db;
            font-weight: bold;
            position: absolute;
            left: -15px;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .tech-item {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .feature-list {
            columns: 2;
            column-gap: 30px;
        }
        @media print {
            body {
                margin: 20px;
            }
            .section {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <h1>Astroloji ve Kişisel Takip Uygulaması Raporu</h1>

    <div class="section">
        <h2>1. Proje Gereksinimleri</h2>
        
        <h3>1.1 Fonksiyonel Gereksinimler</h3>
        <ul>
            <li>Kullanıcı kaydı ve girişi</li>
            <li>Astrolojik veri kaydı ve takibi</li>
            <li>Kişisel sağlık ve aktivite takibi</li>
            <li>Günlük burç yorumları</li>
            <li>Tarot kartı okumaları</li>
            <li>Ay fazı takibi</li>
            <li>Kişisel notlar ve günlük tutma</li>
        </ul>

        <h3>1.2 Teknik Gereksinimler</h3>
        <ul>
            <li>Güvenli kullanıcı kimlik doğrulama</li>
            <li>Veri tabanı entegrasyonu</li>
            <li>Web tabanlı arayüz</li>
            <li>Responsive tasarım</li>
            <li>Veri doğrulama ve validasyon</li>
        </ul>
    </div>

    <div class="section">
        <h2>2. Kullanılan Model</h2>
        
        <h3>2.1 Veri Modelleri</h3>
        <div class="feature-list">
            <div>
                <h4>User Modeli</h4>
                <ul>
                    <li>Kullanıcı kimlik bilgileri</li>
                    <li>Profil bilgileri</li>
                    <li>Güvenlik ayarları</li>
                </ul>
            </div>
            <div>
                <h4>AstrologyData Modeli</h4>
                <ul>
                    <li>Burç yorumları</li>
                    <li>Tarot kartı bilgileri</li>
                    <li>Ay fazı</li>
                    <li>Şanslı sayılar ve renkler</li>
                    <li>Uyumluluk işaretleri</li>
                    <li>Günlük tavsiyeler</li>
                    <li>Yıldız haritası notları</li>
                </ul>
            </div>
            <div>
                <h4>PersonalTracking Modeli</h4>
                <ul>
                    <li>Kilo takibi</li>
                    <li>Hedef vücut bölgesi</li>
                    <li>Diyet notları</li>
                    <li>Aktivite notları</li>
                    <li>Ruh hali</li>
                    <li>Enerji seviyesi</li>
                    <li>Uyku süresi</li>
                    <li>Su tüketimi</li>
                    <li>Egzersiz bilgileri</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>3. Projenin Amacı</h2>
        <p>Bu proje, kullanıcıların astrolojik verilerini ve kişisel sağlık/aktivite takiplerini tek bir platformda birleştirmeyi amaçlamaktadır. Kullanıcılar:</p>
        <ul>
            <li>Günlük burç yorumlarını takip edebilir</li>
            <li>Tarot kartı okumaları alabilir</li>
            <li>Kişisel sağlık ve aktivite verilerini kaydedebilir</li>
            <li>İlerlemelerini görsel olarak takip edebilir</li>
            <li>Astrolojik verilerle kişisel gelişimlerini ilişkilendirebilir</li>
        </ul>
    </div>

    <div class="section">
        <h2>4. Kullanılan Teknolojiler</h2>
        
        <div class="tech-stack">
            <div class="tech-item">
                <h3>Backend Teknolojileri</h3>
                <ul>
                    <li>Java 24</li>
                    <li>Spring Boot 3.4.5</li>
                    <li>Spring Security</li>
                    <li>Spring Data JPA</li>
                    <li>Hibernate ORM</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>Frontend Teknolojileri</h3>
                <ul>
                    <li>Thymeleaf Template Engine</li>
                    <li>HTML/CSS</li>
                    <li>JavaScript</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>Veritabanı</h3>
                <ul>
                    <li>SQLite (Ana veritabanı)</li>
                    <li>H2 Database (Geliştirme/Test)</li>
                    <li>MySQL (Alternatif)</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>Güvenlik</h3>
                <ul>
                    <li>Spring Security</li>
                    <li>Form-based authentication</li>
                    <li>Role-based access control</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>5. Proje Çıktıları</h2>
        
        <h3>5.1 Temel Özellikler</h3>
        <div class="feature-list">
            <div>
                <h4>Kullanıcı Yönetimi</h4>
                <ul>
                    <li>Kayıt olma</li>
                    <li>Giriş yapma</li>
                    <li>Profil yönetimi</li>
                </ul>
            </div>
            
            <div>
                <h4>Astroloji Modülü</h4>
                <ul>
                    <li>Günlük burç yorumları</li>
                    <li>Tarot kartı okumaları</li>
                    <li>Ay fazı takibi</li>
                    <li>Uyumluluk analizleri</li>
                </ul>
            </div>
            
            <div>
                <h4>Kişisel Takip Modülü</h4>
                <ul>
                    <li>Sağlık metrikleri takibi</li>
                    <li>Aktivite kaydı</li>
                    <li>Diyet takibi</li>
                    <li>Uyku ve su tüketimi takibi</li>
                </ul>
            </div>
        </div>

        <h3>5.2 Teknik Çıktılar</h3>
        <ul>
            <li>RESTful API endpoints</li>
            <li>Güvenli veri depolama</li>
            <li>Responsive web arayüzü</li>
            <li>Veri doğrulama ve validasyon</li>
            <li>Otomatik veri güncelleme</li>
        </ul>
    </div>

    <div class="section">
        <h2>6. Sonuç ve Öneriler</h2>
        
        <h3>6.1 Başarılı Yönler</h3>
        <ul>
            <li>Kapsamlı veri modeli</li>
            <li>Güvenli kimlik doğrulama</li>
            <li>Esnek veritabanı seçenekleri</li>
            <li>Modüler yapı</li>
        </ul>

        <h3>6.2 Geliştirilebilecek Yönler</h3>
        <ul>
            <li>Mobil uygulama desteği</li>
            <li>API dokümantasyonu</li>
            <li>Daha fazla astrolojik özellik</li>
            <li>Gelişmiş raporlama özellikleri</li>
        </ul>
    </div>
</body>
</html> 
package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.StudyTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.StudyTrackingRepository;
import Java_Proje.WebProje.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/study")
public class StudyController {

    @Autowired
    private StudyTrackingRepository studyTrackingRepository;

    @Autowired
    private UserService userService;

    /**
     * Ders takip ana sayfası
     */
    @GetMapping
    public String study(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        // Son ders kayıtlarını getir
        List<StudyTracking> recentStudies = studyTrackingRepository.findTop10ByUserOrderByStudyDateDesc(currentUser);

        model.addAttribute("user", currentUser);
        model.addAttribute("currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("dd MMMM yyyy")));
        model.addAttribute("recentStudies", recentStudies);
        model.addAttribute("newStudy", new StudyTracking());

        return "study/index";
    }

    /**
     * Yeni ders kaydı ekleme
     */
    @PostMapping("/add")
    public String addStudy(@Valid @ModelAttribute("newStudy") StudyTracking study,
                          BindingResult bindingResult,
                          @RequestParam(value = "studyDate", required = false) String studyDateStr,
                          RedirectAttributes redirectAttributes) {

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "Lütfen gerekli alanları doldurun.");
            return "redirect:/study";
        }

        try {
            // Her zaman yeni kayıt oluştur
            study.setId(null);

            // Tarih set et
            if (studyDateStr != null && !studyDateStr.isEmpty()) {
                study.setStudyDate(LocalDate.parse(studyDateStr));
            } else {
                study.setStudyDate(LocalDate.now());
            }

            study.setUser(currentUser);
            studyTrackingRepository.save(study);

            redirectAttributes.addFlashAttribute("success", "Ders kaydı başarıyla eklendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Ders kaydı eklenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/study";
    }

    /**
     * Ders kaydı silme
     */
    @PostMapping("/delete/{id}")
    public String deleteStudy(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            Optional<StudyTracking> study = studyTrackingRepository.findById(id);
            if (study.isPresent() && study.get().getUser().getId().equals(currentUser.getId())) {
                studyTrackingRepository.deleteById(id);
                redirectAttributes.addFlashAttribute("success", "Ders kaydı başarıyla silindi!");
            } else {
                redirectAttributes.addFlashAttribute("error", "Kayıt bulunamadı veya yetkiniz yok.");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıt silinirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/study";
    }

    /**
     * Test verilerini temizleme
     */
    @PostMapping("/clear-test-data")
    public String clearTestData(RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }

        try {
            List<StudyTracking> userStudies = studyTrackingRepository.findByUserOrderByStudyDateDesc(currentUser);
            studyTrackingRepository.deleteAll(userStudies);
            redirectAttributes.addFlashAttribute("success", "Tüm ders kayıtları temizlendi!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Kayıtlar temizlenirken hata oluştu: " + e.getMessage());
        }

        return "redirect:/study";
    }

    /**
     * Mevcut oturum açmış kullanıcıyı getirir
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userService.findByUsername(username);
        return userOptional.orElse(null);
    }
}

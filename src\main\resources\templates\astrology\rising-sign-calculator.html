<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> - AsTracker</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Base template ile tutarlı navbar stilleri */
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
            margin: 0 5px;
            border-radius: 8px;
            padding: 8px 16px !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white !important;
        }

        .navbar-brand {
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
        }

        .btn-calculate {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
        }

        .btn-calculate:hover {
            opacity: 0.9;
            color: white;
        }

        .result-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .info-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-star-and-crescent me-2"></i>
                AsTracker
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/dashboard}">Ana Sayfa</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/tracking}">Kişisel Takip</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link active" th:href="@{/astrology}">Astroloji</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/profile}">Profilim</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/login}">Giriş</a>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/register}">Kayıt</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <span class="navbar-text me-3">
                            <i class="fas fa-user me-1"></i>
                            <span sec:authentication="name">Kullanıcı</span>
                        </span>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <form th:action="@{/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>Çıkış
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Başarı/Hata Mesajları -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}">Başarı mesajı</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span th:text="${error}">Hata mesajı</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Başlık -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h2><i class="fas fa-arrow-up me-2"></i>Yükselen Burç Hesaplama</h2>
                        <p class="text-muted">Doğum bilgilerinizi girerek yükselen burcunuzu öğrenin</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hesaplama Formu -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Doğum Bilgileri
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/astrology/calculate-rising-sign}" method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="birthDate" class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Doğum Tarihi
                                        </label>
                                        <input type="date" class="form-control" id="birthDate" name="birthDate" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="birthTime" class="form-label">
                                            <i class="fas fa-clock me-2"></i>Doğum Saati
                                        </label>
                                        <input type="time" class="form-control" id="birthTime" name="birthTime" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="birthCity" class="form-label">
                                            <i class="fas fa-map-marker-alt me-2"></i>Doğum Şehri
                                        </label>
                                        <input type="text" class="form-control" id="birthCity" name="birthCity"
                                               placeholder="Örn: İstanbul" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="birthCountry" class="form-label">
                                            <i class="fas fa-globe me-2"></i>Doğum Ülkesi
                                        </label>
                                        <input type="text" class="form-control" id="birthCountry" name="birthCountry"
                                               placeholder="Türkiye" value="Türkiye" required>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-calculate">
                                    <i class="fas fa-calculator me-2"></i>Yükselen Burcu Hesapla
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sonuç Alanı -->
        <div class="row mt-4" th:if="${risingSign != null}">
            <div class="col-md-8 mx-auto">
                <div class="result-card">
                    <h3><i class="fas fa-star me-2"></i>Yükselen Burcunuz</h3>
                    <h2 th:text="${risingSign}">Burç</h2>
                    <p>Tebrikler! Yükselen burcunuz hesaplandı ve kaydedildi.</p>
                </div>
            </div>
        </div>

        <!-- Bilgi Kartları -->
        <div class="row mt-4" th:if="${risingSignInfo != null}">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Yükselen Burç Bilgileri - <span th:text="${risingSign}">Burç</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-user me-2"></i>Kişilik Özellikleri</h6>
                                    <p th:text="${risingSignInfo.personality}">Kişilik açıklaması</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-star me-2"></i>Yıldız Haritası Etkisi</h6>
                                    <p th:text="${risingSignInfo.chartEffect}">Yıldız haritası açıklaması</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-card">
                                    <h6><i class="fas fa-heart me-2"></i>İlişkilerde</h6>
                                    <p th:text="${risingSignInfo.relationships}">İlişki açıklaması</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-card">
                                    <h6><i class="fas fa-briefcase me-2"></i>Kariyerde</h6>
                                    <p th:text="${risingSignInfo.career}">Kariyer açıklaması</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-card">
                                    <h6><i class="fas fa-palette me-2"></i>Stil & Görünüm</h6>
                                    <p th:text="${risingSignInfo.style}">Stil açıklaması</p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <a th:href="@{/astrology}" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>Astroloji Sayfasına Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

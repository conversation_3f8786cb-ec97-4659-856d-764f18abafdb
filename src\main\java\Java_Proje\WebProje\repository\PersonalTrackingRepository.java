package Java_Proje.WebProje.repository;

import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface PersonalTrackingRepository extends JpaRepository<PersonalTracking, Long> {

    /**
     * Kullanıcıya ait tüm takip kayıtlarını tarihe göre sıralı getirir
     */
    List<PersonalTracking> findByUserOrderByTrackingDateDesc(User user);

    /**
     * Kullanıcıya ait belirli tarih aralığındaki kayıtları getirir
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND pt.trackingDate BETWEEN :startDate AND :endDate ORDER BY pt.trackingDate DESC")
    List<PersonalTracking> findByUserAndDateRange(@Param("user") User user,
                                                  @Param("startDate") LocalDate startDate,
                                                  @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir tarihteki tüm kayıtlarını getirir (aynı tarihte birden fazla kayıt olabilir)
     */
    List<PersonalTracking> findByUserAndTrackingDate(User user, LocalDate trackingDate);

    /**
     * Kullanıcının son N gün kayıtlarını getirir
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND pt.trackingDate >= :fromDate ORDER BY pt.trackingDate DESC")
    List<PersonalTracking> findRecentTrackings(@Param("user") User user, @Param("fromDate") LocalDate fromDate);

    /**
     * Kullanıcının kilo kayıtlarını tarihe göre getirir (sadece kilo olan kayıtlar)
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND pt.weight IS NOT NULL ORDER BY pt.trackingDate DESC")
    List<PersonalTracking> findWeightTrackings(@Param("user") User user);

    /**
     * Kullanıcının son kilo kaydını getirir (SQLite uyumlu)
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND pt.weight IS NOT NULL ORDER BY pt.trackingDate DESC, pt.id DESC")
    List<PersonalTracking> findLatestWeightTrackingList(@Param("user") User user);

    /**
     * Kullanıcının son kilo kaydını getirir (helper metod)
     */
    default Optional<PersonalTracking> findLatestWeightTracking(User user) {
        List<PersonalTracking> trackings = findLatestWeightTrackingList(user);
        return trackings.isEmpty() ? Optional.empty() : Optional.of(trackings.get(0));
    }

    /**
     * Kullanıcının toplam kayıt sayısını döner
     */
    long countByUser(User user);

    /**
     * Kullanıcının bu ayki kayıt sayısını döner
     */
    @Query("SELECT COUNT(pt) FROM PersonalTracking pt WHERE pt.user = :user AND MONTH(pt.trackingDate) = MONTH(CURRENT_DATE) AND YEAR(pt.trackingDate) = YEAR(CURRENT_DATE)")
    long countThisMonthByUser(@Param("user") User user);

    /**
     * Kullanıcının son 10 kaydını getirir
     */
    @Query(value = "SELECT * FROM personal_tracking WHERE user_id = :#{#user.id} ORDER BY tracking_date DESC LIMIT 10", nativeQuery = true)
    List<PersonalTracking> findTop10ByUserOrderByTrackingDateDesc(@Param("user") User user);

    /**
     * Kullanıcının tüm kayıtlarını getirir (sınırsız)
     */
    @Query(value = "SELECT * FROM personal_tracking WHERE user_id = :#{#user.id} ORDER BY tracking_date DESC", nativeQuery = true)
    List<PersonalTracking> findTop3ByUserOrderByTrackingDateDesc(@Param("user") User user);

    /**
     * Kullanıcının sadece hızlı kayıt verilerini getirir (kilo olmayan kayıtlar)
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND (pt.targetBodyPart IS NOT NULL OR pt.waterIntake IS NOT NULL OR pt.exerciseDuration IS NOT NULL OR pt.notes IS NOT NULL) ORDER BY pt.trackingDate DESC")
    List<PersonalTracking> findQuickTrackingsByUser(@Param("user") User user);

    /**
     * Kullanıcının belirli bir tarihteki kayıt sayısını döner
     */
    @Query("SELECT COUNT(pt) FROM PersonalTracking pt WHERE pt.user = :user AND pt.trackingDate = :date")
    long countByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının belirli tarih aralığındaki her gün için kayıt sayılarını döner
     */
    @Query("SELECT pt.trackingDate, COUNT(pt) FROM PersonalTracking pt WHERE pt.user = :user AND pt.trackingDate BETWEEN :startDate AND :endDate GROUP BY pt.trackingDate ORDER BY pt.trackingDate")
    List<Object[]> countByUserAndDateRange(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir yıl ve aydaki tüm kayıtlarını getirir
     */
    @Query("SELECT pt FROM PersonalTracking pt WHERE pt.user = :user AND YEAR(pt.trackingDate) = :year AND MONTH(pt.trackingDate) = :month ORDER BY pt.trackingDate DESC")
    List<PersonalTracking> findByUserAndYearMonth(@Param("user") User user, @Param("year") int year, @Param("month") int month);
}

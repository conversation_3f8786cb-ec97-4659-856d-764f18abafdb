/* <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> - Custom Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #fd7e14;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(45deg, #fd7e14, #ffc107);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
    --border-radius: 15px;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Utility Classes */
.gradient-bg {
    background: var(--gradient-primary);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

/* Card Styles */
.custom-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.custom-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-gradient {
    background: var(--gradient-primary);
    color: white;
}

/* Button Styles */
.btn-gradient {
    background: var(--gradient-primary);
    border: none;
    color: white;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-gradient-secondary {
    background: var(--gradient-secondary);
    border: none;
    color: white;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-gradient-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

/* Form Styles */
.form-floating > .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating > label {
    color: #6c757d;
}

/* Navigation Styles */
.navbar-custom {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.2);
    box-shadow: var(--shadow-light);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

/* Zodiac Styles */
.zodiac-symbol {
    font-size: 2rem;
    color: var(--accent-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.zodiac-card {
    background: var(--gradient-secondary);
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-light);
}

/* Stats Styles */
.stat-card {
    background: var(--glass-effect);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

.float-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px);
    }
    50% { 
        transform: translateY(-20px);
    }
}

/* Loading Spinner */
.spinner-custom {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Styles */
.alert-custom {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

/* Footer Styles */
.footer-custom {
    background: rgba(33,37,41,0.95);
    backdrop-filter: blur(10px);
    color: white;
    padding: 30px 0;
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }
    
    .hero-subtitle {
        font-size: 1.1rem !important;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
    
    .feature-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem !important;
    }
    
    .btn-hero {
        display: block;
        margin: 10px 0;
        width: 100%;
    }
    
    .quick-action-btn {
        display: block;
        margin: 10px 0;
        width: 100%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .glass-effect {
        background: rgba(33,37,41,0.9);
        color: white;
    }
    
    .custom-card {
        background: rgba(33,37,41,0.9);
        color: white;
    }
    
    .form-floating > .form-control {
        background: rgba(33,37,41,0.9);
        color: white;
        border-color: #495057;
    }
    
    .form-floating > label {
        color: #adb5bd;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer-custom,
    .btn,
    .quick-action-btn {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .custom-card,
    .stat-card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

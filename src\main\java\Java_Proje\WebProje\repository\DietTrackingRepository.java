package Java_Proje.WebProje.repository;

import Java_Proje.WebProje.model.DietTracking;
import Java_Proje.WebProje.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DietTrackingRepository extends JpaRepository<DietTracking, Long> {

    /**
     * Kullanıcıya ait tüm diyet takip kayıtlarını tarihe göre sıralı getirir
     */
    List<DietTracking> findByUserOrderByDietDateDesc(User user);

    /**
     * Kullanıcıya ait belirli tarih aralığındaki diyet kayıtlarını getirir
     */
    @Query("SELECT dt FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate BETWEEN :startDate AND :endDate ORDER BY dt.dietDate DESC")
    List<DietTracking> findByUserAndDateRange(@Param("user") User user,
                                             @Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir tarihteki tüm diyet kayıtlarını getirir
     */
    List<DietTracking> findByUserAndDietDate(User user, LocalDate dietDate);

    /**
     * Kullanıcının son N gün diyet kayıtlarını getirir
     */
    @Query("SELECT dt FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate >= :fromDate ORDER BY dt.dietDate DESC")
    List<DietTracking> findRecentDietTrackings(@Param("user") User user, @Param("fromDate") LocalDate fromDate);

    /**
     * Kullanıcının toplam diyet kayıt sayısını döner
     */
    long countByUser(User user);

    /**
     * Kullanıcının bu ayki diyet kayıt sayısını döner
     */
    @Query("SELECT COUNT(dt) FROM DietTracking dt WHERE dt.user = :user AND MONTH(dt.dietDate) = MONTH(CURRENT_DATE) AND YEAR(dt.dietDate) = YEAR(CURRENT_DATE)")
    long countThisMonthByUser(@Param("user") User user);

    /**
     * Kullanıcının son 10 diyet kaydını getirir
     */
    @Query(value = "SELECT * FROM diet_tracking WHERE user_id = :#{#user.id} ORDER BY diet_date DESC LIMIT 10", nativeQuery = true)
    List<DietTracking> findTop10ByUserOrderByDietDateDesc(@Param("user") User user);

    /**
     * Kullanıcının belirli bir tarihteki diyet kayıt sayısını döner
     */
    @Query("SELECT COUNT(dt) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate = :date")
    long countByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının belirli tarih aralığındaki her gün için diyet kayıt sayılarını döner
     */
    @Query("SELECT dt.dietDate, COUNT(dt) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate BETWEEN :startDate AND :endDate GROUP BY dt.dietDate ORDER BY dt.dietDate")
    List<Object[]> countByUserAndDateRange(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Kullanıcının belirli bir yıl ve aydaki tüm diyet kayıtlarını getirir
     */
    @Query("SELECT dt FROM DietTracking dt WHERE dt.user = :user AND YEAR(dt.dietDate) = :year AND MONTH(dt.dietDate) = :month ORDER BY dt.dietDate DESC")
    List<DietTracking> findByUserAndYearMonth(@Param("user") User user, @Param("year") int year, @Param("month") int month);

    /**
     * Kullanıcının öğün türüne göre kayıtlarını getirir
     */
    @Query("SELECT dt FROM DietTracking dt WHERE dt.user = :user AND dt.mealType = :mealType ORDER BY dt.dietDate DESC")
    List<DietTracking> findByUserAndMealType(@Param("user") User user, @Param("mealType") String mealType);

    /**
     * Kullanıcının günlük toplam kalori alımını hesaplar
     */
    @Query("SELECT SUM(dt.calories) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate = :date")
    Double getTotalCaloriesByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının günlük toplam protein alımını hesaplar
     */
    @Query("SELECT SUM(dt.protein) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate = :date")
    Double getTotalProteinByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının günlük toplam karbonhidrat alımını hesaplar
     */
    @Query("SELECT SUM(dt.carbohydrates) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate = :date")
    Double getTotalCarbohydratesByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);

    /**
     * Kullanıcının günlük toplam yağ alımını hesaplar
     */
    @Query("SELECT SUM(dt.fat) FROM DietTracking dt WHERE dt.user = :user AND dt.dietDate = :date")
    Double getTotalFatByUserAndDate(@Param("user") User user, @Param("date") LocalDate date);
}

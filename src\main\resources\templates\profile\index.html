<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profilim - AsTracker</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <!-- CSRF Meta Tags -->
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .profile-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 30px;
            text-align: center;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }

        .info-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1.1rem;
            color: #333;
        }

        .btn-profile {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn-profile:hover {
            opacity: 0.9;
            color: white;
        }

        .btn-profile.btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333) !important;
        }

        .btn-profile.btn-danger:hover {
            background: linear-gradient(45deg, #c82333, #bd2130) !important;
            opacity: 0.9;
        }

        .tracking-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .bmi-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .bmi-normal { background: #28a745; color: white; }
        .bmi-underweight { background: #17a2b8; color: white; }
        .bmi-overweight { background: #ffc107; color: black; }
        .bmi-obese { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Profil Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h2 th:text="${user.firstName + ' ' + user.lastName}">Ad Soyad</h2>
                        <p th:text="${user.email}"><EMAIL></p>
                    </div>
                    <div class="card-body text-center">
                        <a th:href="@{/profile/edit}" class="btn-profile">
                            <i class="fas fa-edit me-2"></i>Profili Düzenle
                        </a>
                        <a th:href="@{/profile/change-password}" class="btn-profile">
                            <i class="fas fa-key me-2"></i>Şifre Değiştir
                        </a>
                        <button type="button" class="btn-profile btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                            <i class="fas fa-trash me-2"></i>Hesabı Sil
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kişisel Bilgiler -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-circle me-2"></i>
                            Kişisel Bilgiler
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Ad</div>
                                    <div class="info-value" th:text="${user.firstName}">Ad</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Soyad</div>
                                    <div class="info-value" th:text="${user.lastName}">Soyad</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Yaş</div>
                                    <div class="info-value" th:text="${userStats.age}">Yaş</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Cinsiyet</div>
                                    <div class="info-value" th:text="${userStats.gender}">Cinsiyet</div>
                                </div>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">E-posta</div>
                            <div class="info-value" th:text="${user.email}">Email</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">Kullanıcı Adı</div>
                            <div class="info-value" th:text="${user.username}">Username</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Fiziksel Bilgiler
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Boy</div>
                                    <div class="info-value" th:text="${userStats.height != 'Henüz kayıt oluşturulmadı' ? userStats.height + ' cm' : userStats.height}">Boy</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Kilo</div>
                                    <div class="info-value" th:text="${userStats.weight != 'Henüz kayıt oluşturulmadı' ? userStats.weight + ' kg' : userStats.weight}">Kilo</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">BMI</div>
                                    <div class="info-value" th:text="${userStats.containsKey('bmi') and userStats.bmi != null ? userStats.bmi : 'Henüz kayıt oluşturulmadı'}">BMI</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">BMI Kategorisi</div>
                                    <div class="info-value">
                                        <span th:if="${userStats.containsKey('bmiCategory') and userStats.bmiCategory != null}"
                                              th:class="${'bmi-badge bmi-' + (userStats.bmiCategory == 'Normal' ? 'normal' :
                                                         userStats.bmiCategory == 'Zayıf' ? 'underweight' :
                                                         userStats.bmiCategory == 'Fazla Kilolu' ? 'overweight' : 'obese')}"
                                              th:text="${userStats.bmiCategory}">Kategori</span>
                                        <span th:if="${!userStats.containsKey('bmiCategory') or userStats.bmiCategory == null}" class="text-muted">Henüz kayıt oluşturulmadı</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Bel Çevresi</div>
                                    <div class="info-value" th:text="${userStats.waist != 'Henüz kayıt oluşturulmadı' ? userStats.waist + ' cm' : userStats.waist}">Bel</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Göğüs Çevresi</div>
                                    <div class="info-value" th:text="${userStats.chest != 'Henüz kayıt oluşturulmadı' ? userStats.chest + ' cm' : userStats.chest}">Göğüs</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Aktivite Seviyesi</div>
                                    <div class="info-value" th:text="${userStats.activityLevel}">Aktivite</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Günlük Kalori İhtiyacı</div>
                                    <div class="info-value" th:text="${userStats.containsKey('dailyCalories') and userStats.dailyCalories != null ? userStats.dailyCalories + ' kcal' : 'Henüz kayıt oluşturulmadı'}">Kalori</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Astroloji Bilgileri -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star-and-crescent me-2"></i>
                            Astroloji Bilgileri
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Burç</div>
                                    <div class="info-value" th:text="${userStats.zodiacSign}">Burç</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Yükselen Burç</div>
                                    <div class="info-value" th:text="${userStats.risingSign}">Yükselen</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <a th:href="@{/astrology}" class="btn btn-primary btn-sm">
                                <i class="fas fa-star me-2"></i>Astroloji Sayfası
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Son Kilo Kaydı
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${userStats.weight != 'Henüz kayıt oluşturulmadı'}">
                            <div class="info-card">
                                <div class="info-label">Son Kilo</div>
                                <div class="info-value" th:text="${userStats.weight + ' kg'}">Kilo</div>
                            </div>
                            <div class="info-card">
                                <div class="info-label">Kayıt Tarihi</div>
                                <div class="info-value" th:text="${userStats.lastWeightDate}">Tarih</div>
                            </div>
                        </div>
                        <div th:unless="${userStats.weight != 'Henüz kayıt oluşturulmadı'}" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            Henüz kayıt oluşturulmadı
                        </div>
                        <div class="text-center mt-3">
                            <a th:href="@{/tracking}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-2"></i>Veri Girişi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Hesap Silme Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteAccountModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Hesabı Sil
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form th:action="@{/profile/delete-account}" method="post">
                    <div class="modal-body">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>DİKKAT!</strong> Bu işlem geri alınamaz. Hesabınız ve tüm verileriniz kalıcı olarak silinecektir.
                        </div>

                        <div class="mb-3">
                            <label for="deletePassword" class="form-label">
                                <i class="fas fa-lock me-2"></i>Şifrenizi girin:
                            </label>
                            <input type="password" class="form-control" id="deletePassword" name="password"
                                   placeholder="Mevcut şifrenizi girin" required>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label text-danger" for="confirmDelete">
                                Hesabımı kalıcı olarak silmek istediğimi onaylıyorum
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>İptal
                        </button>
                        <button type="submit" class="btn btn-danger" id="deleteAccountBtn" disabled>
                            <i class="fas fa-trash me-2"></i>Hesabı Sil
                        </button>
                    </div>
                    <!-- CSRF Token -->
                    <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Profil kartları animasyonu
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });

            // BMI badge animasyonu
            const bmiBadge = document.querySelector('.bmi-badge');
            if (bmiBadge) {
                setTimeout(() => {
                    bmiBadge.style.animation = 'pulse 2s infinite';
                }, 1000);
            }

            // Profil butonları hover efekti
            const profileButtons = document.querySelectorAll('.btn-profile');
            profileButtons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Hesap silme modal kontrolü
            const confirmDeleteCheckbox = document.getElementById('confirmDelete');
            const deleteAccountBtn = document.getElementById('deleteAccountBtn');
            const deletePasswordInput = document.getElementById('deletePassword');

            function updateDeleteButton() {
                const isChecked = confirmDeleteCheckbox.checked;
                const hasPassword = deletePasswordInput.value.length > 0;
                deleteAccountBtn.disabled = !(isChecked && hasPassword);
            }

            if (confirmDeleteCheckbox && deleteAccountBtn && deletePasswordInput) {
                confirmDeleteCheckbox.addEventListener('change', updateDeleteButton);
                deletePasswordInput.addEventListener('input', updateDeleteButton);

                // Modal açıldığında formu temizle
                const deleteModal = document.getElementById('deleteAccountModal');
                deleteModal.addEventListener('show.bs.modal', function() {
                    deletePasswordInput.value = '';
                    confirmDeleteCheckbox.checked = false;
                    deleteAccountBtn.disabled = true;
                });
            }
        });

        // Pulse animasyonu için CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>Proje</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Navigation için beyaz arka plan */
        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .zodiac-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .zodiac-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .zodiac-card.active {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .zodiac-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .trait-detail {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-top: 20px;
        }

        .element-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .element-fire { background: linear-gradient(45deg, #ff6b6b, #ffa726); color: white; }
        .element-earth { background: linear-gradient(45deg, #8bc34a, #4caf50); color: white; }
        .element-air { background: linear-gradient(45deg, #42a5f5, #29b6f6); color: white; }
        .element-water { background: linear-gradient(45deg, #26c6da, #00acc1); color: white; }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-user-astronaut me-3"></i>
                            Burç Özellikleri
                        </h1>
                        <p class="lead">Her burcun kendine özgü özelliklerini keşfedin</p>
                        <a href="/astrology" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Ana Sayfaya Dön
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Burç Seçimi -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            Burç Seçin
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="zodiac-grid">
                            <div class="zodiac-card" onclick="showZodiacInfo('Koç', '🐏')">
                                <div class="zodiac-icon">🐏</div>
                                <h6>Koç</h6>
                                <small class="text-muted">21 Mart - 19 Nisan</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Boğa', '🐂')">
                                <div class="zodiac-icon">🐂</div>
                                <h6>Boğa</h6>
                                <small class="text-muted">20 Nisan - 20 Mayıs</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('İkizler', '👯')">
                                <div class="zodiac-icon">👯</div>
                                <h6>İkizler</h6>
                                <small class="text-muted">21 Mayıs - 20 Haziran</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Yengeç', '🦀')">
                                <div class="zodiac-icon">🦀</div>
                                <h6>Yengeç</h6>
                                <small class="text-muted">21 Haziran - 22 Temmuz</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Aslan', '🦁')">
                                <div class="zodiac-icon">🦁</div>
                                <h6>Aslan</h6>
                                <small class="text-muted">23 Temmuz - 22 Ağustos</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Başak', '👩')">
                                <div class="zodiac-icon">👩</div>
                                <h6>Başak</h6>
                                <small class="text-muted">23 Ağustos - 22 Eylül</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Terazi', '⚖️')">
                                <div class="zodiac-icon">⚖️</div>
                                <h6>Terazi</h6>
                                <small class="text-muted">23 Eylül - 22 Ekim</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Akrep', '🦂')">
                                <div class="zodiac-icon">🦂</div>
                                <h6>Akrep</h6>
                                <small class="text-muted">23 Ekim - 21 Kasım</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Yay', '🏹')">
                                <div class="zodiac-icon">🏹</div>
                                <h6>Yay</h6>
                                <small class="text-muted">22 Kasım - 21 Aralık</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Oğlak', '🐐')">
                                <div class="zodiac-icon">🐐</div>
                                <h6>Oğlak</h6>
                                <small class="text-muted">22 Aralık - 19 Ocak</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Kova', '🏺')">
                                <div class="zodiac-icon">🏺</div>
                                <h6>Kova</h6>
                                <small class="text-muted">20 Ocak - 18 Şubat</small>
                            </div>

                            <div class="zodiac-card" onclick="showZodiacInfo('Balık', '🐟')">
                                <div class="zodiac-icon">🐟</div>
                                <h6>Balık</h6>
                                <small class="text-muted">19 Şubat - 20 Mart</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Burç Detayları -->
        <div class="row">
            <div class="col-12">
                <div class="card" id="zodiac-details" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="selected-zodiac-name">Burç</span> Özellikleri
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="content-card">
                            <div class="text-center mb-4">
                                <div style="font-size: 4rem;" id="zodiac-emoji">🐏</div>
                                <h3 id="zodiac-name">Burç Adı</h3>
                                <span class="badge bg-primary" id="zodiac-element">Element</span>
                            </div>

                            <h5>Kişilik Özellikleri</h5>
                            <p class="lead" id="zodiac-traits">Özellikler</p>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-plus-circle text-success me-2"></i>Güçlü Yanları</h6>
                                    <ul class="list-unstyled" id="zodiac-strengths">
                                        <li>• Güçlü yanlar</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>Dikkat Edilmesi Gerekenler</h6>
                                    <ul class="list-unstyled" id="zodiac-weaknesses">
                                        <li>• Dikkat edilecekler</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        const zodiacData = {
            'Koç': {
                emoji: '🐏',
                element: 'Ateş',
                traits: 'Enerjik, cesur ve lider ruhlu. Yeni başlangıçları sever, hızlı karar verir ve girişimcidir.',
                strengths: ['Liderlik yeteneği', 'Cesaret ve kararlılık', 'Enerjik ve dinamik', 'Girişimci ruh'],
                weaknesses: ['Aceleci davranışlar', 'Sabırsızlık', 'Öfkeli olabilir', 'Düşünmeden hareket eder']
            },
            'Boğa': {
                emoji: '🐂',
                element: 'Toprak',
                traits: 'Sabırlı, güvenilir ve kararlı. Güvenlik ve istikrarı sever, pratik ve gerçekçidir.',
                strengths: ['Güvenilirlik', 'Sabır ve dayanıklılık', 'Pratik zeka', 'Sadakat'],
                weaknesses: ['İnatçılık', 'Değişime direnç', 'Yavaş hareket eder', 'Maddiyata düşkünlük']
            },
            'İkizler': {
                emoji: '👯',
                element: 'Hava',
                traits: 'Meraklı, sosyal ve çok yönlü. İletişim kurmayı sever, hızlı öğrenir ve adapte olur.',
                strengths: ['İletişim becerisi', 'Adaptasyon yeteneği', 'Zeka ve merak', 'Sosyal beceriler'],
                weaknesses: ['Kararsızlık', 'Yüzeysellik', 'Odaklanma sorunu', 'Değişkenlik']
            },
            'Yengeç': {
                emoji: '🦀',
                element: 'Su',
                traits: 'Duygusal, koruyucu ve sezgisel. Aile ve ev önemlidir, empati yeteneği güçlüdür.',
                strengths: ['Empati yeteneği', 'Koruyucu instinkt', 'Sezgisel zeka', 'Sadakat'],
                weaknesses: ['Aşırı duygusallık', 'Geçmişe takılma', 'Savunmacı tavır', 'Mood değişimleri']
            },
            'Aslan': {
                emoji: '🦁',
                element: 'Ateş',
                traits: 'Gururlu, yaratıcı ve cömert. Dikkat çekmeyi sever, doğal lider ve dramatiktir.',
                strengths: ['Doğal liderlik', 'Yaratıcılık', 'Cömertlik', 'Özgüven'],
                weaknesses: ['Kibir', 'Dikkat arayışı', 'Bencillik', 'Aşırı gururluluk']
            },
            'Başak': {
                emoji: '👩',
                element: 'Toprak',
                traits: 'Mükemmeliyetçi, analitik ve hizmet odaklı. Detaylara dikkat eder, organize ve pratiktir.',
                strengths: ['Analitik düşünce', 'Organizasyon', 'Detaycılık', 'Güvenilirlik'],
                weaknesses: ['Aşırı eleştiricilik', 'Mükemmeliyetçilik', 'Endişeli yapı', 'Katılık']
            },
            'Terazi': {
                emoji: '⚖️',
                element: 'Hava',
                traits: 'Dengeli, adil ve estetik. Uyum arar, diplomatik ve sosyaldir.',
                strengths: ['Diplomasi', 'Adalet duygusu', 'Estetik anlayış', 'Sosyal beceriler'],
                weaknesses: ['Kararsızlık', 'Çelişkili davranış', 'Yüzeysellik', 'Bağımlılık']
            },
            'Akrep': {
                emoji: '🦂',
                element: 'Su',
                traits: 'Yoğun, tutkulu ve gizemli. Derinlemesine düşünür, güçlü sezgilere sahiptir.',
                strengths: ['Güçlü sezgi', 'Kararlılık', 'Tutkulu yaklaşım', 'Derin düşünce'],
                weaknesses: ['Kıskançlık', 'İntikamcı', 'Gizli saklı', 'Aşırı şüpheci']
            },
            'Yay': {
                emoji: '🏹',
                element: 'Ateş',
                traits: 'Özgür ruhlu, iyimser ve maceracı. Öğrenmeyi sever, açık sözlü ve felsefidir.',
                strengths: ['İyimserlik', 'Özgürlük sevgisi', 'Açık fikirlilik', 'Macera ruhu'],
                weaknesses: ['Sorumsuzluk', 'Taahhüt korkusu', 'Sabırsızlık', 'Taktik eksikliği']
            },
            'Oğlak': {
                emoji: '🐐',
                element: 'Toprak',
                traits: 'Disiplinli, hırslı ve sorumlu. Başarı odaklı, pratik ve gelenekseldir.',
                strengths: ['Disiplin', 'Hırs ve azim', 'Sorumluluk', 'Pratik zeka'],
                weaknesses: ['Katılık', 'Aşırı ciddiyet', 'Pessimizm', 'Duygusal soğukluk']
            },
            'Kova': {
                emoji: '🏺',
                element: 'Hava',
                traits: 'Özgün, insancıl ve vizyoner. Yenilikçi, bağımsız ve toplum yanlısıdır.',
                strengths: ['Yenilikçilik', 'Bağımsızlık', 'İnsancıl yaklaşım', 'Vizyon'],
                weaknesses: ['Duygusal mesafe', 'İnatçılık', 'Aşırı idealizm', 'Öngörülemezlik']
            },
            'Balık': {
                emoji: '🐟',
                element: 'Su',
                traits: 'Hassas, yaratıcı ve sezgisel. Empati yeteneği güçlü, rüyacı ve şefkatlidir.',
                strengths: ['Empati', 'Yaratıcılık', 'Sezgisel güç', 'Şefkat'],
                weaknesses: ['Aşırı hassaslık', 'Kaçış eğilimi', 'Kararsızlık', 'Kurban rolü']
            }
        };

        function showZodiacInfo(sign, emoji) {
            const data = zodiacData[sign];
            if (!data) return;

            document.getElementById('selected-zodiac-name').textContent = sign;
            document.getElementById('zodiac-emoji').textContent = emoji;
            document.getElementById('zodiac-name').textContent = sign;
            document.getElementById('zodiac-element').textContent = data.element + ' Elementi';
            document.getElementById('zodiac-traits').textContent = data.traits;

            // Güçlü yanları
            const strengthsList = document.getElementById('zodiac-strengths');
            strengthsList.innerHTML = '';
            data.strengths.forEach(strength => {
                const li = document.createElement('li');
                li.textContent = '• ' + strength;
                strengthsList.appendChild(li);
            });

            // Zayıf yanları
            const weaknessesList = document.getElementById('zodiac-weaknesses');
            weaknessesList.innerHTML = '';
            data.weaknesses.forEach(weakness => {
                const li = document.createElement('li');
                li.textContent = '• ' + weakness;
                weaknessesList.appendChild(li);
            });

            // Detayları göster
            document.getElementById('zodiac-details').style.display = 'block';

            // Seçili kartı vurgula
            document.querySelectorAll('.zodiac-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.zodiac-card').classList.add('active');
        }
    </script>
</body>
</html>

spring.application.name=WebProje

# SQLite Database Configuration
spring.datasource.url=****************************
spring.datasource.driver-class-name=org.sqlite.JDBC
spring.datasource.username=
spring.datasource.password=

# JPA Configuration for SQLite
spring.jpa.database-platform=org.hibernate.community.dialect.SQLiteDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# SQLite specific settings
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.dialect.storage_engine=innodb

# H2 Console (Alternative - disabled for SQLite)
spring.h2.console.enabled=false

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Server Configuration
server.port=8080

# Logging
logging.level.org.springframework.security=DEBUG
logging.level.Java_Proje.WebProje=DEBUG

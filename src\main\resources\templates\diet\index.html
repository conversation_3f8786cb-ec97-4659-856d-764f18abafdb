<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diyet Takip - AsTracker</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .diet-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            padding: 12px 15px;
            background: rgba(255,255,255,0.9);
        }

        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            border: none;
            border-radius: 10px;
        }

        .diet-item {
            background: rgba(255,255,255,0.7);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }

        .floating-label {
            position: relative;
        }

        .floating-label input,
        .floating-label select,
        .floating-label textarea {
            padding-top: 20px;
        }

        .floating-label label {
            position: absolute;
            top: 15px;
            left: 15px;
            color: #6c757d;
            transition: all 0.3s ease;
            pointer-events: none;
            background: transparent;
        }

        .floating-label input:focus + label,
        .floating-label input:not(:placeholder-shown) + label,
        .floating-label select:focus + label,
        .floating-label select:not([value=""]) + label,
        .floating-label textarea:focus + label,
        .floating-label textarea:not(:placeholder-shown) + label {
            top: 5px;
            font-size: 0.75rem;
            color: #28a745;
        }

        .summary-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .summary-item {
            margin-bottom: 10px;
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Include Navigation -->
    <div th:replace="~{layout/base :: navigation}"></div>

    <!-- Main Content -->
    <div class="container my-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="diet-card">
                    <div class="card-body text-center">
                        <h2 class="mb-3">
                            <i class="fas fa-utensils me-2 text-primary"></i>
                            Diyet Takip Paneli
                        </h2>
                        <p class="text-muted mb-0" th:text="${currentDate}">Bugün</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Daily Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="summary-card">
                    <h5><i class="fas fa-chart-pie me-2"></i>Günlük Özet</h5>
                    <div class="row">
                        <div class="col-3">
                            <div class="summary-item">
                                <div class="summary-value" th:text="${dailySummary.totalCalories}">0</div>
                                <small>Kalori</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="summary-item">
                                <div class="summary-value" th:text="${dailySummary.totalProtein}">0</div>
                                <small>Protein (g)</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="summary-item">
                                <div class="summary-value" th:text="${dailySummary.totalCarbs}">0</div>
                                <small>Karbonhidrat (g)</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="summary-item">
                                <div class="summary-value" th:text="${dailySummary.totalFat}">0</div>
                                <small>Yağ (g)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Body Measurements -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="diet-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-ruler me-2"></i>
                            Vücut Ölçüleri
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/diet/update-measurements}" method="post">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" name="height" step="0.1" min="100" max="250"
                                               th:value="${dailySummary['height'] != null && !#strings.isEmpty(dailySummary['height']) ? dailySummary['height'] : ''}"
                                               placeholder=" ">
                                        <label>Boy (cm)</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" name="weight" step="0.1" min="30" max="300" placeholder=" ">
                                        <label>Kilo (kg)</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" name="waist" step="0.1" min="50" max="200"
                                               th:value="${dailySummary['waist'] != null && !#strings.isEmpty(dailySummary['waist']) ? dailySummary['waist'] : ''}"
                                               placeholder=" ">
                                        <label>Bel (cm)</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" name="chest" step="0.1" min="60" max="200"
                                               th:value="${dailySummary['chest'] != null && !#strings.isEmpty(dailySummary['chest']) ? dailySummary['chest'] : ''}"
                                               placeholder=" ">
                                        <label>Göğüs (cm)</label>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Güncelle
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Diet Entry -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="diet-card">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            Hızlı Diyet Kaydı
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/diet/add}" method="post" th:object="${newDiet}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="date" class="form-control" th:field="*{dietDate}" required>
                                        <label>Tarih</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <select class="form-select" th:field="*{mealType}">
                                            <option value="">Seçiniz</option>
                                            <option value="Kahvaltı">Kahvaltı</option>
                                            <option value="Ara Öğün">Ara Öğün</option>
                                            <option value="Öğle">Öğle</option>
                                            <option value="İkindi">İkindi</option>
                                            <option value="Akşam">Akşam</option>
                                            <option value="Gece">Gece</option>
                                        </select>
                                        <label>Öğün</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="text" class="form-control" th:field="*{foodName}" placeholder=" " required>
                                        <label>Yemek Adı</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="floating-label">
                                        <input type="text" class="form-control" th:field="*{portionSize}" placeholder=" ">
                                        <label>Porsiyon</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" th:field="*{calories}" step="0.1" min="0" placeholder=" ">
                                        <label>Kalori</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" th:field="*{protein}" step="0.1" min="0" placeholder=" ">
                                        <label>Protein (g)</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" th:field="*{carbohydrates}" step="0.1" min="0" placeholder=" ">
                                        <label>Karbonhidrat (g)</label>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="floating-label">
                                        <input type="number" class="form-control" th:field="*{fat}" step="0.1" min="0" placeholder=" ">
                                        <label>Yağ (g)</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="floating-label">
                                        <textarea class="form-control" th:field="*{dietNotes}" rows="3" placeholder=" "></textarea>
                                        <label>Notlar</label>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Kaydet
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Diets -->
        <div class="row" th:if="${!#lists.isEmpty(recentDiets)}">
            <div class="col-12">
                <div class="diet-card">
                    <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            Son Diyet Kayıtları
                        </h5>
                        <form th:action="@{/diet/clear-test-data}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-danger btn-sm" 
                                    onclick="return confirm('Tüm diyet kayıtlarını silmek istediğinizden emin misiniz?')">
                                <i class="fas fa-trash me-1"></i>Kayıtları Sil
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tarih</th>
                                        <th>Öğün</th>
                                        <th>Yemek</th>
                                        <th>Kalori</th>
                                        <th>Protein</th>
                                        <th>İşlem</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="diet : ${recentDiets}">
                                        <td th:text="${#temporals.format(diet.dietDate, 'dd/MM/yyyy')}">01/01/2024</td>
                                        <td th:text="${diet.mealType ?: '-'}">Kahvaltı</td>
                                        <td th:text="${diet.foodName}">Yumurta</td>
                                        <td th:text="${diet.calories != null ? diet.calories + ' kcal' : '-'}">150 kcal</td>
                                        <td th:text="${diet.protein != null ? diet.protein + ' g' : '-'}">12 g</td>
                                        <td>
                                            <form th:action="@{/diet/delete/{id}(id=${diet.id})}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                        onclick="return confirm('Bu kaydı silmek istediğinizden emin misiniz?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Data Message -->
        <div class="row" th:if="${#lists.isEmpty(recentDiets)}">
            <div class="col-12">
                <div class="diet-card">
                    <div class="card-body text-center">
                        <i class="fas fa-utensils" style="font-size: 4rem; color: #28a745; margin-bottom: 20px;"></i>
                        <h5>Henüz diyet kaydı bulunmuyor</h5>
                        <p class="text-muted">Yukarıdaki formu kullanarak ilk diyet kaydınızı oluşturun.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSRF Meta Tags -->
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>
</body>
</html>

package Java_Proje.WebProje.controller;

import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
public class HomeController {

    @Autowired
    private UserService userService;

    /**
     * Ana sayfa
     */
    @GetMapping({"/", "/home"})
    public String home() {
        return "index";
    }

    /**
     * <PERSON><PERSON><PERSON> sayfası
     */
    @GetMapping("/login")
    public String login(@RequestParam(value = "error", required = false) String error,
                       @RequestParam(value = "logout", required = false) String logout,
                       Model model) {
        
        if (error != null) {
            model.addAttribute("error", "Kullanıcı adı veya şifre hatalı!");
        }
        
        if (logout != null) {
            model.addAttribute("message", "Başarıyla çıkış yaptınız.");
        }
        
        return "auth/login";
    }

    /**
     * Kayıt sayfası - GET
     */
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "auth/register";
    }

    /**
     * Kayıt sayfası - POST
     */
    @PostMapping("/register")
    public String registerUser(@Valid @ModelAttribute("user") User user,
                              BindingResult bindingResult,
                              @RequestParam("confirmPassword") String confirmPassword,
                              Model model,
                              RedirectAttributes redirectAttributes) {
        
        // Validation hataları varsa
        if (bindingResult.hasErrors()) {
            return "auth/register";
        }
        
        // Şifre onayı kontrolü
        if (!user.getPassword().equals(confirmPassword)) {
            model.addAttribute("passwordError", "Şifreler eşleşmiyor!");
            return "auth/register";
        }
        
        try {
            // Kullanıcıyı kaydet
            userService.registerUser(user);
            redirectAttributes.addFlashAttribute("success", 
                "Kayıt başarılı! Şimdi giriş yapabilirsiniz.");
            return "redirect:/login";
            
        } catch (RuntimeException e) {
            model.addAttribute("error", e.getMessage());
            return "auth/register";
        }
    }

    /**
     * Hakkında sayfası
     */
    @GetMapping("/about")
    public String about() {
        return "about";
    }

    /**
     * İletişim sayfası
     */
    @GetMapping("/contact")
    public String contact() {
        return "contact";
    }
}

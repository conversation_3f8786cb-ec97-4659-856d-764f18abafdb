package Java_Proje.WebProje.service;

import Java_Proje.WebProje.model.PersonalTracking;
import Java_Proje.WebProje.model.User;
import Java_Proje.WebProje.repository.PersonalTrackingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class PersonalTrackingService {

    @Autowired
    private PersonalTrackingRepository trackingRepository;

    @Autowired
    private EntityManager entityManager;

    /**
     * Yeni tracking kaydı kaydet - Her zaman yeni kayıt oluştur
     */
    public PersonalTracking saveTracking(PersonalTracking tracking) {
        // Her zaman yeni kayıt oluştur (aynı tarihte birden fazla kayıt olabilir)
        PersonalTracking saved = trackingRepository.save(tracking);
        // Hemen veritabanına yaz (flush)
        entityManager.flush();
        return saved;
    }

    /**
     * Kullanıcının tüm kayıtlarını getir
     */
    public List<PersonalTracking> getAllTrackings(User user) {
        return trackingRepository.findByUserOrderByTrackingDateDesc(user);
    }

    /**
     * Kullanıcının belirli tarih aralığındaki kayıtlarını getir
     */
    public List<PersonalTracking> getTrackingsByDateRange(User user, LocalDate startDate, LocalDate endDate) {
        return trackingRepository.findByUserAndDateRange(user, startDate, endDate);
    }



    /**
     * Kullanıcının son N gün kayıtlarını getir
     */
    public List<PersonalTracking> getRecentTrackings(User user, int days) {
        LocalDate fromDate = LocalDate.now().minusDays(days);
        return trackingRepository.findRecentTrackings(user, fromDate);
    }

    /**
     * Kullanıcının kilo kayıtlarını getir
     */
    public List<PersonalTracking> getWeightTrackings(User user) {
        return trackingRepository.findWeightTrackings(user);
    }

    /**
     * Kullanıcının son kilo kaydını getir
     */
    public Optional<PersonalTracking> getLatestWeightTracking(User user) {
        return trackingRepository.findLatestWeightTracking(user);
    }

    /**
     * Kullanıcının toplam kayıt sayısını getir
     */
    public long getTotalTrackingCount(User user) {
        return trackingRepository.countByUser(user);
    }

    /**
     * Kullanıcının bu ayki kayıt sayısını getir
     */
    public long getThisMonthTrackingCount(User user) {
        return trackingRepository.countThisMonthByUser(user);
    }

    /**
     * Tracking kaydını sil
     */
    public void deleteTracking(Long trackingId, User user) {
        Optional<PersonalTracking> tracking = trackingRepository.findById(trackingId);
        if (tracking.isPresent() && tracking.get().getUser().getId().equals(user.getId())) {
            trackingRepository.delete(tracking.get());
        }
    }

    /**
     * Tracking kaydını ID ile getir (kullanıcı kontrolü ile)
     */
    public Optional<PersonalTracking> getTrackingById(Long trackingId, User user) {
        Optional<PersonalTracking> tracking = trackingRepository.findById(trackingId);
        if (tracking.isPresent() && tracking.get().getUser().getId().equals(user.getId())) {
            return tracking;
        }
        return Optional.empty();
    }



    /**
     * Haftalık istatistikleri hesapla
     */
    public TrackingStats getWeeklyStats(User user) {
        LocalDate startOfWeek = LocalDate.now().minusDays(LocalDate.now().getDayOfWeek().getValue() - 1);
        LocalDate endOfWeek = startOfWeek.plusDays(6);

        List<PersonalTracking> weeklyTrackings = getTrackingsByDateRange(user, startOfWeek, endOfWeek);

        return calculateStats(weeklyTrackings);
    }

    /**
     * Aylık istatistikleri hesapla
     */
    public TrackingStats getMonthlyStats(User user) {
        LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
        LocalDate endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());

        List<PersonalTracking> monthlyTrackings = getTrackingsByDateRange(user, startOfMonth, endOfMonth);

        return calculateStats(monthlyTrackings);
    }



    /**
     * İstatistikleri hesapla
     */
    private TrackingStats calculateStats(List<PersonalTracking> trackings) {
        TrackingStats stats = new TrackingStats();

        if (trackings.isEmpty()) {
            return stats;
        }

        stats.setTotalDays(trackings.size());

        // Ortalama değerleri hesapla
        double totalWater = 0;
        double totalExercise = 0;
        double totalSleep = 0;
        int waterCount = 0;
        int exerciseCount = 0;
        int sleepCount = 0;

        for (PersonalTracking tracking : trackings) {
            if (tracking.getWaterIntake() != null) {
                totalWater += tracking.getWaterIntake();
                waterCount++;
            }
            if (tracking.getExerciseDuration() != null) {
                totalExercise += tracking.getExerciseDuration();
                exerciseCount++;
            }
            if (tracking.getSleepHours() != null) {
                totalSleep += tracking.getSleepHours().doubleValue();
                sleepCount++;
            }
        }

        stats.setAverageWaterIntake(waterCount > 0 ? totalWater / waterCount : 0);
        stats.setAverageExerciseDuration(exerciseCount > 0 ? totalExercise / exerciseCount : 0);
        stats.setAverageSleepHours(sleepCount > 0 ? totalSleep / sleepCount : 0);

        return stats;
    }

    /**
     * İstatistik sınıfı
     */
    public static class TrackingStats {
        private int totalDays;
        private double averageWaterIntake;
        private double averageExerciseDuration;
        private double averageSleepHours;

        // Getters and Setters
        public int getTotalDays() { return totalDays; }
        public void setTotalDays(int totalDays) { this.totalDays = totalDays; }

        public double getAverageWaterIntake() { return averageWaterIntake; }
        public void setAverageWaterIntake(double averageWaterIntake) { this.averageWaterIntake = averageWaterIntake; }

        public double getAverageExerciseDuration() { return averageExerciseDuration; }
        public void setAverageExerciseDuration(double averageExerciseDuration) { this.averageExerciseDuration = averageExerciseDuration; }

        public double getAverageSleepHours() { return averageSleepHours; }
        public void setAverageSleepHours(double averageSleepHours) { this.averageSleepHours = averageSleepHours; }
    }
}

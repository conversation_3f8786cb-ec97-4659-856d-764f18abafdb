package Java_Proje.WebProje.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "astrology_data")
public class AstrologyData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @NotNull(message = "Tarih boş olamaz")
    @Column(name = "data_date", nullable = false)
    private LocalDate dataDate;
    
    @Column(name = "horoscope_reading", columnDefinition = "TEXT")
    private String horoscopeReading;
    
    @Column(name = "tarot_card")
    private String tarotCard;
    
    @Column(name = "tarot_meaning", columnDefinition = "TEXT")
    private String tarotMeaning;
    
    @Column(name = "moon_phase")
    private String moonPhase;
    
    @Column(name = "lucky_numbers")
    private String luckyNumbers;
    
    @Column(name = "lucky_colors")
    private String luckyColors;
    
    @Column(name = "compatibility_sign")
    private String compatibilitySign;
    
    @Column(name = "daily_advice", columnDefinition = "TEXT")
    private String dailyAdvice;
    
    @Column(name = "star_chart_notes", columnDefinition = "TEXT")
    private String starChartNotes;
    
    @Column(name = "personal_notes", columnDefinition = "TEXT")
    private String personalNotes;
    
    @Column(name = "mood_prediction")
    private String moodPrediction;
    
    @Column(name = "energy_forecast")
    private String energyForecast;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public AstrologyData() {}
    
    public AstrologyData(User user, LocalDate dataDate) {
        this.user = user;
        this.dataDate = dataDate;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public LocalDate getDataDate() {
        return dataDate;
    }
    
    public void setDataDate(LocalDate dataDate) {
        this.dataDate = dataDate;
    }
    
    public String getHoroscopeReading() {
        return horoscopeReading;
    }
    
    public void setHoroscopeReading(String horoscopeReading) {
        this.horoscopeReading = horoscopeReading;
    }
    
    public String getTarotCard() {
        return tarotCard;
    }
    
    public void setTarotCard(String tarotCard) {
        this.tarotCard = tarotCard;
    }
    
    public String getTarotMeaning() {
        return tarotMeaning;
    }
    
    public void setTarotMeaning(String tarotMeaning) {
        this.tarotMeaning = tarotMeaning;
    }
    
    public String getMoonPhase() {
        return moonPhase;
    }
    
    public void setMoonPhase(String moonPhase) {
        this.moonPhase = moonPhase;
    }
    
    public String getLuckyNumbers() {
        return luckyNumbers;
    }
    
    public void setLuckyNumbers(String luckyNumbers) {
        this.luckyNumbers = luckyNumbers;
    }
    
    public String getLuckyColors() {
        return luckyColors;
    }
    
    public void setLuckyColors(String luckyColors) {
        this.luckyColors = luckyColors;
    }
    
    public String getCompatibilitySign() {
        return compatibilitySign;
    }
    
    public void setCompatibilitySign(String compatibilitySign) {
        this.compatibilitySign = compatibilitySign;
    }
    
    public String getDailyAdvice() {
        return dailyAdvice;
    }
    
    public void setDailyAdvice(String dailyAdvice) {
        this.dailyAdvice = dailyAdvice;
    }
    
    public String getStarChartNotes() {
        return starChartNotes;
    }
    
    public void setStarChartNotes(String starChartNotes) {
        this.starChartNotes = starChartNotes;
    }
    
    public String getPersonalNotes() {
        return personalNotes;
    }
    
    public void setPersonalNotes(String personalNotes) {
        this.personalNotes = personalNotes;
    }
    
    public String getMoodPrediction() {
        return moodPrediction;
    }
    
    public void setMoodPrediction(String moodPrediction) {
        this.moodPrediction = moodPrediction;
    }
    
    public String getEnergyForecast() {
        return energyForecast;
    }
    
    public void setEnergyForecast(String energyForecast) {
        this.energyForecast = energyForecast;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}

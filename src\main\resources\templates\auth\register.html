<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Astroloji & <PERSON><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .register-card {
            background: rgba(255,255,255,0.95);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .register-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-weight: 600;
        }
        
        .register-body {
            padding: 30px;
        }
        
        .form-floating {
            margin-bottom: 15px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
        
        .zodiac-info {
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="card register-card">
                <div class="register-header">
                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                    <h2>Hesap Oluştur</h2>
                    <p class="mb-0">Yolculuğunuza başlayın</p>
                </div>
                
                <div class="register-body">
                    <!-- Error Messages -->
                    <div th:if="${error}" class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span th:text="${error}">Hata mesajı</span>
                    </div>
                    
                    <div th:if="${passwordError}" class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span th:text="${passwordError}">Şifre hatası</span>
                    </div>
                    
                    <!-- Registration Form -->
                    <form th:action="@{/register}" method="post" th:object="${user}" id="registerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="firstName" 
                                           th:field="*{firstName}" placeholder="Ad">
                                    <label for="firstName">
                                        <i class="fas fa-user me-2"></i>Ad
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="lastName" 
                                           th:field="*{lastName}" placeholder="Soyad">
                                    <label for="lastName">
                                        <i class="fas fa-user me-2"></i>Soyad
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" 
                                   th:field="*{username}" placeholder="Kullanıcı adı" required>
                            <label for="username">
                                <i class="fas fa-at me-2"></i>Kullanıcı Adı
                            </label>
                            <div th:if="${#fields.hasErrors('username')}" class="text-danger small mt-1">
                                <span th:errors="*{username}"></span>
                            </div>
                        </div>
                        
                        <div class="form-floating">
                            <input type="email" class="form-control" id="email" 
                                   th:field="*{email}" placeholder="Email" required>
                            <label for="email">
                                <i class="fas fa-envelope me-2"></i>Email
                            </label>
                            <div th:if="${#fields.hasErrors('email')}" class="text-danger small mt-1">
                                <span th:errors="*{email}"></span>
                            </div>
                        </div>
                        
                        <div class="form-floating">
                            <input type="date" class="form-control" id="birthDate" 
                                   th:field="*{birthDate}" placeholder="Doğum tarihi">
                            <label for="birthDate">
                                <i class="fas fa-calendar me-2"></i>Doğum Tarihi
                            </label>
                        </div>
                        
                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" 
                                   th:field="*{password}" placeholder="Şifre" required>
                            <label for="password">
                                <i class="fas fa-lock me-2"></i>Şifre
                            </label>
                            <div class="password-strength" id="passwordStrength"></div>
                            <div th:if="${#fields.hasErrors('password')}" class="text-danger small mt-1">
                                <span th:errors="*{password}"></span>
                            </div>
                        </div>
                        
                        <div class="form-floating">
                            <input type="password" class="form-control" id="confirmPassword" 
                                   name="confirmPassword" placeholder="Şifre tekrar" required>
                            <label for="confirmPassword">
                                <i class="fas fa-lock me-2"></i>Şifre Tekrar
                            </label>
                        </div>
                        
                        <div class="zodiac-info" id="zodiacInfo" style="display: none;">
                            <i class="fas fa-star me-2"></i>
                            <span id="zodiacText">Burç bilginiz otomatik hesaplanacak</span>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-register mt-3">
                            <i class="fas fa-user-plus me-2"></i>Hesap Oluştur
                        </button>
                    </form>
                    
                    <div class="login-link">
                        <p class="mb-0">Zaten hesabınız var mı? 
                            <a th:href="@{/login}">Giriş yapın</a>
                        </p>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a th:href="@{/}" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-2"></i>Ana sayfaya dön
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            strengthBar.className = 'password-strength';
            if (strength < 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength < 4) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });
        
        // Zodiac sign calculator
        document.getElementById('birthDate').addEventListener('change', function() {
            const birthDate = new Date(this.value);
            if (birthDate) {
                const zodiac = calculateZodiacSign(birthDate);
                const zodiacInfo = document.getElementById('zodiacInfo');
                const zodiacText = document.getElementById('zodiacText');
                
                if (zodiac) {
                    zodiacText.textContent = `Burcunuz: ${zodiac}`;
                    zodiacInfo.style.display = 'block';
                }
            }
        });
        
        function calculateZodiacSign(date) {
            const month = date.getMonth() + 1;
            const day = date.getDate();
            
            if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) return "Koç";
            if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) return "Boğa";
            if ((month == 5 && day >= 21) || (month == 6 && day <= 20)) return "İkizler";
            if ((month == 6 && day >= 21) || (month == 7 && day <= 22)) return "Yengeç";
            if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) return "Aslan";
            if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) return "Başak";
            if ((month == 9 && day >= 23) || (month == 10 && day <= 22)) return "Terazi";
            if ((month == 10 && day >= 23) || (month == 11 && day <= 21)) return "Akrep";
            if ((month == 11 && day >= 22) || (month == 12 && day <= 21)) return "Yay";
            if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) return "Oğlak";
            if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) return "Kova";
            if ((month == 2 && day >= 19) || (month == 3 && day <= 20)) return "Balık";
            
            return null;
        }
        
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Şifreler eşleşmiyor!');
            }
        });
    </script>
</body>
</html>
